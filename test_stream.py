#!/usr/bin/env python3
"""
Backend Stream Test - Direct Cloud Run Testing
This bypasses Cloudflare completely to test if the Python backend streams correctly.
"""

import requests
import time
import json

# IMPORTANT: Direct Cloud Run URL (bypasses <PERSON>flare)
BACKEND_URL = "https://doctor-recep-api-340621766769.asia-south1.run.app/api/generate-summary-stream"

# Test payload (minimal valid request)
test_payload = {
    "primary_audio_url": "https://celerai.tallyup.pro/test-audio.wav",
    "additional_audio_urls": [],
    "image_urls": [],
    "submitted_by": "doctor",
    "consultation_type": "outpatient",
    "patient_name": "Test Patient"
}

print(f"🎯 Testing direct backend streaming...")
print(f"📡 URL: {BACKEND_URL}")
print(f"📦 Payload: {json.dumps(test_payload, indent=2)}")
print("=" * 60)

try:
    # Use stream=True to prevent requests from buffering the response
    with requests.post(
        BACKEND_URL, 
        json=test_payload,
        headers={'Content-Type': 'application/json'},
        stream=True,
        timeout=30
    ) as response:
        
        print(f"📊 Status Code: {response.status_code}")
        print("📋 Response Headers:")
        for key, value in response.headers.items():
            print(f"   {key}: {value}")
        print("=" * 60)

        if response.status_code != 200:
            print(f"❌ Bad status code: {response.status_code}")
            print(f"📄 Response text: {response.text}")
            exit(1)

        print("🚀 Starting stream consumption...")
        chunk_count = 0
        start_time = time.time()
        
        # Read in small chunks to detect streaming
        for chunk in response.iter_content(chunk_size=16):
            if chunk:
                chunk_count += 1
                elapsed = time.time() - start_time
                chunk_text = chunk.decode('utf-8', errors='ignore')
                print(f"📦 Chunk {chunk_count} at {elapsed:.2f}s: {repr(chunk_text)}")
                
                # If we get more than 10 chunks, the streaming is working
                if chunk_count > 10:
                    print("✅ STREAMING IS WORKING - Backend streams correctly!")
                    break
        
        total_time = time.time() - start_time
        print("=" * 60)
        print(f"✅ Stream test completed in {total_time:.2f}s with {chunk_count} chunks")

except requests.exceptions.Timeout:
    print("❌ TIMEOUT - Backend took too long to respond")
except requests.exceptions.ConnectionError as e:
    print(f"❌ CONNECTION ERROR - Cannot reach backend: {e}")
except requests.exceptions.RequestException as e:
    print(f"❌ REQUEST ERROR: {e}")
except Exception as e:
    print(f"❌ UNEXPECTED ERROR: {e}")

print("\n🔍 DIAGNOSIS:")
print("- If you see multiple chunks with timestamps: ✅ Backend streaming works")
print("- If you see one big chunk at the end: ❌ Backend is buffering")
print("- If you see timeout/connection errors: ❌ Backend is not accessible")
