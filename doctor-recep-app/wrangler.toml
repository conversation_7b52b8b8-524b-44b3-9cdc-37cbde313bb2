# Cloudflare Workers Configuration for Celer AI
# This file configures the deployment of doctor-recep-app to Cloudflare Workers

# Worker name - will appear in Cloudflare dashboard
name = "celer-ai-app"

# Entry point for the application (generated by OpenNext)
main = ".open-next/worker.js"

# Compatibility settings for modern features and Node.js compatibility
compatibility_date = "2025-06-25"
compatibility_flags = ["nodejs_compat"]

# Enable logging for debugging and monitoring
[observability]
enabled = true
head_sampling_rate = 1

# Static assets configuration
[assets]
# Directory containing static files (CSS, JS, images, etc.)
directory = ".open-next/assets"
# Internal binding name - DO NOT CHANGE
binding = "ASSETS"

# KV Storage binding for quota management and job tracking
[[kv_namespaces]]
binding = "CELERAI_KV"
id = "d3b80332c5cc46669aeecfdaa649c7b5"

# Durable Objects configuration for streaming
[durable_objects]
bindings = [
  { name = "SUMMARY_STREAM_HANDLER", class_name = "SummaryStreamHandler" }
]

# Durable Objects migration
[[migrations]]
tag = "v1"
new_classes = ["SummaryStreamHandler"]



# Public environment variables (non-sensitive)
[vars]
# These will be available as process.env.* in your application
NODE_ENV = "production"

# Note: All sensitive environment variables (API keys, tokens, passwords)
# must be set as secrets using:
# wrangler secret put VARIABLE_NAME
# 
# Required secrets to set:
# - NEXT_PUBLIC_SUPABASE_URL
# - NEXT_PUBLIC_SUPABASE_ANON_KEY  
# - SUPABASE_SERVICE_ROLE_KEY
# - SESSION_SECRET
# - VERIFYNOW_AUTH_TOKEN
# - VERIFYNOW_CUSTOMER_ID
# - WEBHOOK_SECRET_TOKEN
# - INTERNAL_API_TOKEN
# - RESEND_API_KEY
# Redis credentials no longer needed - migrated to Cloudflare KV
# - UPSTASH_REDIS_REST_URL (REMOVED)
# - UPSTASH_REDIS_REST_TOKEN (REMOVED)
# - R2_ACCESS_KEY_ID
# - R2_SECRET_ACCESS_KEY
# - R2_ACCOUNT_ID
# - R2_BUCKET_NAME
# - R2_PUBLIC_URL
# - NEXT_PUBLIC_API_URL
# - GEMINI_API_KEY
# - NEXT_PUBLIC_SANITY_PROJECT_ID
# - NEXT_PUBLIC_SANITY_DATASET
# - NEXT_PUBLIC_SANITY_TOKEN

# Custom domain configuration
[[routes]]
pattern = "worker.celerai.live"
custom_domain = true

# Development configuration
[env.development]
name = "celer-ai-app-dev"
vars = { NODE_ENV = "development" }

# R2 Storage binding for development
[[env.development.r2_buckets]]
binding = "CELERAI_BUCKET"
bucket_name = "celerai-storage"

# KV Storage binding for development
[[env.development.kv_namespaces]]
binding = "CELERAI_KV"
id = "d3b80332c5cc46669aeecfdaa649c7b5"

# Production configuration
[env.production]
name = "celer-ai-app-prod"
vars = { NODE_ENV = "production" }

# R2 Storage binding for production
[[env.production.r2_buckets]]
binding = "CELERAI_BUCKET"
bucket_name = "celerai-storage"

# KV Storage binding for production
[[env.production.kv_namespaces]]
binding = "CELERAI_KV"
id = "d3b80332c5cc46669aeecfdaa649c7b5"

# Durable Objects configuration for production
[env.production.durable_objects]
bindings = [
  { name = "SUMMARY_STREAM_HANDLER", class_name = "SummaryStreamHandler" }
]
