#!/usr/bin/env node

/**
 * 🔧 POST-BUILD RESTORATION SCRIPT
 * 
 * This script runs after `npm run build:cf` to restore custom files
 * that get deleted by the OpenNext.js build process.
 * 
 * Problem: OpenNext.js recreates .open-next folder from scratch,
 * deleting our custom Durable Objects files.
 * 
 * Solution: Automatically restore custom files after each build.
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Restoring custom files after OpenNext.js build...');

// Define custom files to restore
const customFiles = [
  {
    source: 'scripts/templates/summary-stream-handler.js',
    destination: '.open-next/durable-objects/summary-stream-handler.js'
  }
];

// Create directories and restore files
customFiles.forEach(({ source, destination }) => {
  try {
    // Create destination directory if it doesn't exist
    const destDir = path.dirname(destination);
    if (!fs.existsSync(destDir)) {
      fs.mkdirSync(destDir, { recursive: true });
      console.log(`📁 Created directory: ${destDir}`);
    }

    // Copy file from template
    if (fs.existsSync(source)) {
      fs.copyFileSync(source, destination);
      console.log(`✅ Restored: ${destination}`);
    } else {
      console.error(`❌ Template not found: ${source}`);
      process.exit(1);
    }
  } catch (error) {
    console.error(`❌ Failed to restore ${destination}:`, error.message);
    process.exit(1);
  }
});

console.log('🎉 Custom files restoration complete!');
console.log('💡 You can now run: wrangler deploy --env production');
