// 🚀 CUSTOM WORKER: Import generated OpenNext.js worker and add streaming interceptor
// @ts-ignore `.open-next/worker.js` is generated at build time
import { default as handler } from "./.open-next/worker.js";

// 🚀 DURABLE OBJECTS: Re-export OpenNext.js default DOs + our custom streaming handler
// @ts-ignore `.open-next/worker.js` is generated at build time
export { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DOShardedTagCache, BucketCachePurge } from "./.open-next/worker.js";
// 🚀 OUR CUSTOM STREAMING HANDLER
export { SummaryStreamHandler } from "./.open-next/durable-objects/summary-stream-handler.js";

export default {
    async fetch(request, env, ctx) {
        const url = new URL(request.url);

        // 🚀 STREAMING INTERCEPTOR: Route streaming requests to Durable Objects
        // This bypasses OpenNext.js to prevent SSE corruption
        if (url.pathname === '/api/generate-summary-stream' && request.method === 'POST') {
            console.log('🎯 Intercepting streaming request for Durable Objects');

            // 🚀 MULTITENANT: Extract user ID from session for user-specific Durable Objects
            const userId = await extractUserIdFromRequest(request, env);
            if (!userId) {
                console.log('❌ No user ID found in request');
                return new Response(JSON.stringify({ error: 'Unauthorized' }), {
                    status: 401,
                    headers: { 'Content-Type': 'application/json' }
                });
            }

            // 🎯 USER-SPECIFIC DURABLE OBJECT: Each user gets their own DO instance
            const durableObjectId = env.SUMMARY_STREAM_HANDLER.idFromName(`stream-${userId}`);
            const durableObject = env.SUMMARY_STREAM_HANDLER.get(durableObjectId);

            console.log('🚀 Routing to user-specific Durable Object:', `stream-${userId}`);
            return durableObject.fetch(request);
        }

        // 🚀 For all other requests, use the original OpenNext.js handler
        return handler.fetch(request, env, ctx);
    },
};

// 🔐 HELPER: Extract user ID from request session for multitenancy
async function extractUserIdFromRequest(request, env) {
    try {
        // Parse cookies from request
        const cookieHeader = request.headers.get('Cookie');
        if (!cookieHeader) {
            console.log('❌ No cookies found in request');
            return null;
        }

        // Extract all cookies with proper URL decoding
        const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
            const [key, value] = cookie.trim().split('=');
            if (key && value) {
                acc[key] = decodeURIComponent(value);
            }
            return acc;
        }, {});

        // 🚀 SUPABASE SSR: Look for actual Supabase cookie names
        // Supabase SSR uses project-specific cookie names like:
        // sb-edojplwmwtytpdssrxbh-auth-token (access token)

        let sessionCookie = null;
        const supabaseProjectRef = 'edojplwmwtytpdssrxbh'; // Your project ref

        // Try different possible Supabase cookie patterns
        const possibleCookieNames = [
            `sb-${supabaseProjectRef}-auth-token`,
            `sb-${supabaseProjectRef}-auth-token-code-verifier`,
            'sb-access-token',
            'supabase-auth-token',
            'sb-auth-token'
        ];

        for (const cookieName of possibleCookieNames) {
            if (cookies[cookieName]) {
                sessionCookie = cookies[cookieName];
                console.log('✅ Found session cookie:', cookieName);
                break;
            }
        }

        if (!sessionCookie) {
            console.log('❌ No Supabase session cookie found. Available cookies:', Object.keys(cookies));
            return null;
        }

        // 🚀 SUPABASE SSR: Parse the session data properly
        // The cookie contains JSON-encoded session data, not a raw JWT
        try {
            const sessionData = JSON.parse(sessionCookie);

            // Extract access_token from session data
            const accessToken = sessionData.access_token;
            if (!accessToken) {
                console.log('❌ No access_token found in session data');
                return null;
            }

            // Now decode the JWT access token
            const payload = JSON.parse(atob(accessToken.split('.')[1]));
            const userId = payload.sub;

            console.log('✅ Extracted user ID from session:', userId);
            return userId;
        } catch (parseError) {
            // Fallback: try to decode as direct JWT (for backward compatibility)
            console.log('⚠️ Trying fallback JWT decode...');
            try {
                const payload = JSON.parse(atob(sessionCookie.split('.')[1]));
                const userId = payload.sub;
                console.log('✅ Extracted user ID from fallback JWT:', userId);
                return userId;
            } catch (jwtError) {
                console.error('❌ Failed to parse session cookie as JSON or JWT:', parseError.message, jwtError.message);
                return null;
            }
        }
    } catch (error) {
        console.error('❌ Error extracting user ID:', error);
        return null;
    }
}
