// 🚀 CUSTOM WORKER: Import generated OpenNext.js worker and add streaming interceptor
// @ts-ignore `.open-next/worker.js` is generated at build time
import { default as handler } from "./.open-next/worker.js";

// 🚀 DURABLE OBJECTS: Re-export OpenNext.js default DOs + our custom streaming handler
// @ts-ignore `.open-next/worker.js` is generated at build time
export { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DOShardedTagCache, BucketCachePurge } from "./.open-next/worker.js";
// 🚀 OUR CUSTOM STREAMING HANDLER
export { SummaryStreamHandler } from "./.open-next/durable-objects/summary-stream-handler.js";

export default {
    async fetch(request, env, ctx) {
        const url = new URL(request.url);

        // 🚀 STREAMING INTERCEPTOR: Route streaming requests to Durable Objects
        // This bypasses OpenNext.js to prevent SSE corruption
        if (url.pathname === '/api/generate-summary-stream' && request.method === 'POST') {
            console.log('🎯 Intercepting streaming request for Durable Objects');

            // 🚀 MULTITENANT: Extract user ID from session for user-specific Durable Objects
            const userId = await extractUserIdFromRequest(request, env);
            if (!userId) {
                console.log('❌ No user ID found in request');
                return new Response(JSON.stringify({ error: 'Unauthorized' }), {
                    status: 401,
                    headers: { 'Content-Type': 'application/json' }
                });
            }

            // 🎯 USER-SPECIFIC DURABLE OBJECT: Each user gets their own DO instance
            const durableObjectId = env.SUMMARY_STREAM_HANDLER.idFromName(`stream-${userId}`);
            const durableObject = env.SUMMARY_STREAM_HANDLER.get(durableObjectId);

            console.log('🚀 Routing to user-specific Durable Object:', `stream-${userId}`);
            return durableObject.fetch(request);
        }

        // 🚀 For all other requests, use the original OpenNext.js handler
        return handler.fetch(request, env, ctx);
    },
};

// 🔐 HELPER: Extract user ID from request session for multitenancy
async function extractUserIdFromRequest(request, env) {
    try {
        // Parse cookies from request
        const cookieHeader = request.headers.get('Cookie');
        if (!cookieHeader) {
            console.log('❌ No cookies found in request');
            return null;
        }

        // Extract session cookie (adjust cookie name based on your Supabase setup)
        const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
            const [key, value] = cookie.trim().split('=');
            acc[key] = value;
            return acc;
        }, {});

        // Look for Supabase session cookies
        const sessionCookie = cookies['sb-access-token'] || cookies['supabase-auth-token'] || cookies['sb-auth-token'];
        if (!sessionCookie) {
            console.log('❌ No session cookie found');
            return null;
        }

        // Decode JWT to get user ID (basic extraction - in production you might want to verify signature)
        const payload = JSON.parse(atob(sessionCookie.split('.')[1]));
        const userId = payload.sub;

        console.log('✅ Extracted user ID from session:', userId);
        return userId;
    } catch (error) {
        console.error('❌ Error extracting user ID:', error);
        return null;
    }
}
