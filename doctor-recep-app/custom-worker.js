// 🚀 CUSTOM WORKER: Import generated OpenNext.js worker and add streaming interceptor
// @ts-ignore `.open-next/worker.js` is generated at build time
import { default as handler } from "./.open-next/worker.js";

// 🚀 DURABLE OBJECTS: Re-export OpenNext.js default DOs + our custom streaming handler
// @ts-ignore `.open-next/worker.js` is generated at build time
export { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DOShardedTagCache, BucketCachePurge } from "./.open-next/worker.js";
// 🚀 OUR CUSTOM STREAMING HANDLER
export { SummaryStreamHandler } from "./.open-next/durable-objects/summary-stream-handler.js";

export default {
    async fetch(request, env, ctx) {
        const url = new URL(request.url);

        // 🚀 STREAMING INTERCEPTOR: Route streaming requests to Durable Objects
        // This bypasses OpenNext.js to prevent SSE corruption
        if (url.pathname === '/api/generate-summary-stream' && request.method === 'POST') {
            console.log('🎯 Intercepting streaming request for Durable Objects');
            const durableObjectId = env.SUMMARY_STREAM_HANDLER.idFromName('summary-stream');
            const durableObject = env.SUMMARY_STREAM_HANDLER.get(durableObjectId);
            return durableObject.fetch(request);
        }

        // 🚀 For all other requests, use the original OpenNext.js handler
        return handler.fetch(request, env, ctx);
    },
};
