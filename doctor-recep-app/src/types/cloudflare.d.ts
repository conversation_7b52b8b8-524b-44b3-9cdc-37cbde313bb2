// Cloudflare Workers type declarations for KV and R2 bindings

declare global {
  // Cloudflare KV namespace binding
  const CELERAI_KV: KVNamespace

  // Cloudflare R2 bucket binding  
  const CELERAI_BUCKET: R2Bucket

  // Environment interface for Cloudflare Workers
  interface CloudflareEnv {
    CELERAI_KV: KVNamespace
    CELERAI_BUCKET: R2Bucket
    
    // Secrets (environment variables)
    NEXT_PUBLIC_SUPABASE_URL: string
    NEXT_PUBLIC_SUPABASE_ANON_KEY: string
    SUPABASE_SERVICE_ROLE_KEY: string
    SESSION_SECRET: string
    VERIFYNOW_AUTH_TOKEN: string
    VERIFYNOW_CUSTOMER_ID: string
    WEBHOOK_SECRET_TOKEN: string
    INTERNAL_API_TOKEN: string
    RESEND_API_KEY: string
    R2_ACCESS_KEY_ID: string
    R2_SECRET_ACCESS_KEY: string
    R2_ACCOUNT_ID: string
    R2_BUCKET_NAME: string
    R2_PUBLIC_URL: string
    NEXT_PUBLIC_API_URL: string
    GEMINI_API_KEY: string
    NEXT_PUBLIC_SANITY_PROJECT_ID: string
    NEXT_PUBLIC_SANITY_DATASET: string
    NEXT_PUBLIC_SANITY_TOKEN: string
  }
}

// Extend the global namespace for Node.js compatibility
declare namespace NodeJS {
  interface ProcessEnv extends Partial<CloudflareEnv> {
    NODE_ENV: 'development' | 'production' | 'test'
  }
}

export {}
