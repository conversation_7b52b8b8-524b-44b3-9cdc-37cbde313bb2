import { NextRequest } from 'next/server'
import { verifySession } from '@/lib/auth/supabase-helpers'
import { checkQuotaKV, incrementQuotaKV, setJobStatus } from '@/lib/kv'
import { incrementQuotaUsage } from '@/lib/actions/quota'
import { v4 as uuidv4 } from 'uuid'

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Starting generate-summary-stream API call')

    // Verify user session and check quota before proceeding
    console.log('🔐 Verifying user session...')
    const session = await verifySession()
    if (!session) {
      console.log('❌ Session verification failed')
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      })
    }
    console.log('✅ Session verified for user:', session.userId)

    // Generate unique job ID for tracking
    const jobId = uuidv4()
    console.log('📝 Generated job ID:', jobId)

    // KV-first quota check for instant response
    console.log('📊 Checking quota with KV...')
    const quotaCheck = await checkQuotaKV(session.userId)
    console.log('📊 Quota check result:', quotaCheck)

    if (!quotaCheck.allowed) {
      return new Response(JSON.stringify({
        error: 'Quota exceeded. You have reached your monthly AI generation limit. Please contact admin or wait for next month.'
      }), {
        status: 429,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    // Create job tracking record in KV
    console.log('📋 Creating job tracking record...')
    const jobData = {
      jobId,
      userId: session.userId,
      type: 'ai_generation',
      status: 'pending',
      createdAt: new Date().toISOString(),
      metadata: {
        quotaBeforeIncrement: quotaCheck.currentUsage
      }
    }

    // Store job with 30-minute TTL
    console.log('💾 Storing job status in KV...')
    await setJobStatus(jobId, jobData, 1800)
    console.log('✅ Job status stored successfully')

    // Optimistically increment quota in KV (instant)
    console.log('⬆️ Incrementing quota in KV...')
    try {
      await incrementQuotaKV(session.userId)
      console.log('✅ Quota incremented successfully')
    } catch (error) {
      console.error('❌ KV quota increment failed:', error)
      // Update job status to reflect the error
      await setJobStatus(jobId, {
        ...jobData,
        status: 'failed',
        error: 'Quota increment failed',
        completedAt: new Date().toISOString()
      }, 1800)
      // Continue anyway - we'll sync with database later
    }

    // Fire-and-forget database quota update (async) with job tracking
    incrementQuotaUsage(session.userId)
      .then(async () => {
        // Update job status to indicate successful database sync
        const updatedJobData = {
          ...jobData,
          status: 'processing',
          metadata: {
            ...jobData.metadata,
            databaseSyncCompleted: true,
            databaseSyncAt: new Date().toISOString()
          }
        }
        await setJobStatus(jobId, updatedJobData, 1800)
      })
      .catch(async (error) => {
        console.error('Background database quota update failed:', error)
        // Update job status to reflect database sync failure
        const failedJobData = {
          ...jobData,
          status: 'failed',
          error: 'Database quota update failed',
          completedAt: new Date().toISOString(),
          metadata: {
            ...jobData.metadata,
            databaseSyncFailed: true,
            databaseSyncError: error.message
          }
        }
        await setJobStatus(jobId, failedJobData, 3600) // Keep failed jobs longer
      })

    const body = await request.json()

    console.log('🔄 Worker - Received request body:', {
      primary_audio_url: body.primary_audio_url ? '✅ Present' : '❌ Missing',
      additional_audio_urls: body.additional_audio_urls?.length || 0,
      image_urls: body.image_urls?.length || 0,
      submitted_by: body.submitted_by,
      consultation_type: body.consultation_type,
      patient_name: body.patient_name
    })

    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://doctor-recep-api-340621766769.asia-south1.run.app'
    console.log('🔄 Worker - Forwarding to:', `${apiUrl}/api/generate-summary-stream`)

    // Forward the request to the Python backend
    const response = await fetch(`${apiUrl}/api/generate-summary-stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    })

    console.log('🔄 Next.js API Route - Backend response status:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('🔄 Worker - Backend error:', errorText)
      return new Response(JSON.stringify({
        error: 'Failed to connect to backend service',
        details: errorText
      }), {
        status: response.status,
        headers: { 'Content-Type': 'application/json' }
      })
    }

    // 🚀 MAGIC HANDSHAKE: Direct stream pipe with Cloudflare streaming optimization
    // The 'text/event-stream' content type is the magic signal to Cloudflare that this is
    // a Server-Sent Event stream that should bypass all buffering and processing
    return new Response(response.body, {
      status: response.status, // Pass through backend status code
      headers: {
        'Content-Type': 'text/event-stream; charset=utf-8', // 🎯 MAGIC HANDSHAKE
        'Cache-Control': 'no-cache', 
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
        'X-Job-ID': jobId, // Include job ID in response headers for frontend tracking
      },
    })
  } catch (error) {
    console.error('Streaming API error:', error)
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}