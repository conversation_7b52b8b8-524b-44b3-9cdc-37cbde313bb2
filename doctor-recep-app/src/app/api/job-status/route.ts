import { NextRequest, NextResponse } from 'next/server'
import { verifySession } from '@/lib/auth/supabase-helpers'
import { getJobStatus, setJobStatus } from '@/lib/kv'

/**
 * Job Status API - Part of the UI Reconciliation for Optimistic Updates
 *
 * This endpoint allows the frontend to poll for the status of background
 * database operations after optimistic updates.
 */
export async function GET(request: NextRequest) {
  try {
    // Verify user session
    const session = await verifySession()
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const jobId = searchParams.get('id')

    if (!jobId) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      )
    }

    // Get job status from KV
    const job = await getJobStatus(jobId)

    if (!job) {
      return NextResponse.json(
        { error: 'Job not found or expired' },
        { status: 404 }
      )
    }

    // Validate job ownership (security check)
    if (job.userId !== session.userId) {
      return NextResponse.json(
        { error: 'Unauthorized access to job' },
        { status: 403 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        jobId: job.jobId,
        status: job.status,
        type: job.type,
        createdAt: job.createdAt,
        completedAt: job.completedAt,
        error: job.error,
        result: job.result,
        metadata: job.metadata
      }
    })

  } catch (error) {
    console.error('Job status check error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * Update job status - Used internally by background processes
 */
export async function PATCH(request: NextRequest) {
  try {
    // This endpoint should only be called internally
    // In production, you might want to add additional security checks
    const authHeader = request.headers.get('authorization')
    const internalToken = process.env.INTERNAL_API_TOKEN

    if (!internalToken || authHeader !== `Bearer ${internalToken}`) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { jobId, status, error, result, metadata } = body

    if (!jobId || !status) {
      return NextResponse.json(
        { error: 'Job ID and status are required' },
        { status: 400 }
      )
    }

    // Update job status in KV
    // Get existing job data
    const existingJob = await getJobStatus(jobId)
    if (!existingJob) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    // Update job data
    const updatedJob = {
      ...existingJob,
      status,
      error: error || existingJob.error,
      result: result || existingJob.result,
      metadata: { ...existingJob.metadata, ...metadata },
      completedAt: ['completed', 'failed'].includes(status)
        ? new Date().toISOString()
        : existingJob.completedAt
    }

    // Set updated job data with extended TTL for completed jobs
    const ttl = ['completed', 'failed'].includes(status) ? 3600 : 1800 // 1 hour for completed, 30 min for pending
    await setJobStatus(jobId, updatedJob, ttl)

    return NextResponse.json({
      success: true,
      data: updatedJob
    })

  } catch (error) {
    console.error('Job status update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
