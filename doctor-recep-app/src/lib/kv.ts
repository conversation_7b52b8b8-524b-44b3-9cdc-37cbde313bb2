// Cloudflare KV client for quota management and optimistic UI
// Migrated from Redis to Cloudflare KV for better edge performance

// Type definition for KV namespace
interface KVNamespace {
  get(key: string): Promise<string | null>
  put(key: string, value: string, options?: { expirationTtl?: number }): Promise<void>
  delete(key: string): Promise<void>
  list(): Promise<{ keys: Array<{ name: string }> }>
}

// Global variable to store the KV binding passed from the worker
let kvBinding: KVNamespace | null = null

export function setKVBinding(kv: KVNamespace) {
  kvBinding = kv
}

// Get KV namespace from Cloudflare Workers binding (same pattern as R2)
function getKVNamespace(): KVNamespace | null {
  // Try multiple ways to access the KV binding (same as R2 pattern)
  try {
    // Method 1: Through Cloudflare context (OpenNext) - SAME AS R2
    const context = (globalThis as any)[Symbol.for("__cloudflare-context__")];
    if (context?.env?.CELERAI_KV) {
      console.log('✅ CELERAI_KV binding found via Cloudflare context')
      return context.env.CELERAI_KV;
    }

    // Method 2: Direct global access (fallback)
    if ((globalThis as any).CELERAI_KV) {
      console.log('✅ CELERAI_KV binding found in globalThis')
      return (globalThis as any).CELERAI_KV;
    }

    // Method 3: Through env object (if available)
    if ((globalThis as any).env?.CELERAI_KV) {
      console.log('✅ CELERAI_KV binding found in env')
      return (globalThis as any).env.CELERAI_KV;
    }

    console.error('❌ KV binding not found. Available globals:', Object.keys(globalThis).filter(k => k.includes('KV') || k.includes('CELERAI')));
    return null;
  } catch (error) {
    console.error('Error accessing KV namespace:', error);
    return null;
  }
}

export function getKVClient(): KVNamespace {
  console.log('🔍 Checking KV binding availability...')

  const kv = getKVNamespace()
  if (!kv) {
    throw new Error('CELERAI_KV binding not found. Ensure KV namespace is properly configured in wrangler.toml and deployed.')
  }

  return kv
}

// Quota management functions
export async function checkQuotaKV(userId: string): Promise<{ allowed: boolean; currentUsage: number }> {
  try {
    const kv = getKVClient()
    const key = `quota:${userId}`

    // Get current usage from KV
    const currentUsageStr = await kv.get(key)
    const currentUsage = currentUsageStr ? parseInt(currentUsageStr, 10) : 0

    // For now, we'll use a default quota of 50 (this should come from the database)
    // In production, you might want to cache the user's quota in KV as well
    const quota = 50

    return {
      allowed: currentUsage < quota,
      currentUsage
    }
  } catch (error) {
    console.error('KV quota check error:', error)
    // Fallback: allow the request if KV is down
    return { allowed: true, currentUsage: 0 }
  }
}

export async function incrementQuotaKV(userId: string): Promise<number> {
  try {
    const kv = getKVClient()
    const key = `quota:${userId}`

    // KV doesn't have atomic increment, so we need to get + put
    const currentUsageStr = await kv.get(key)
    const currentUsage = currentUsageStr ? parseInt(currentUsageStr, 10) : 0
    const newUsage = currentUsage + 1

    // Calculate expiration to end of month
    const now = new Date()
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
    const secondsUntilEndOfMonth = Math.floor((endOfMonth.getTime() - now.getTime()) / 1000)

    // Store with TTL
    await kv.put(key, newUsage.toString(), { expirationTtl: secondsUntilEndOfMonth })

    return newUsage
  } catch (error) {
    console.error('KV quota increment error:', error)
    throw error
  }
}

export async function resetQuotaKV(userId: string): Promise<void> {
  try {
    const kv = getKVClient()
    const key = `quota:${userId}`
    await kv.delete(key)
  } catch (error) {
    console.error('KV quota reset error:', error)
    throw error
  }
}

// Sync KV quota with database (called periodically or after DB updates)
export async function syncQuotaFromDatabase(userId: string, quotaUsed: number): Promise<void> {
  try {
    const kv = getKVClient()
    const key = `quota:${userId}`

    // Calculate expiration to end of month
    const now = new Date()
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
    const secondsUntilEndOfMonth = Math.floor((endOfMonth.getTime() - now.getTime()) / 1000)

    // Store with TTL
    await kv.put(key, quotaUsed.toString(), { expirationTtl: secondsUntilEndOfMonth })
  } catch (error) {
    console.error('KV quota sync error:', error)
    throw error
  }
}

// Job status tracking functions for optimistic UI updates
export async function setJobStatus(jobId: string, jobData: any, ttlSeconds: number = 1800): Promise<void> {
  try {
    const kv = getKVClient()
    const key = `job:${jobId}`
    await kv.put(key, JSON.stringify(jobData), { expirationTtl: ttlSeconds })
  } catch (error) {
    console.error('KV job status set error:', error)
    throw error
  }
}

export async function getJobStatus(jobId: string): Promise<any | null> {
  try {
    const kv = getKVClient()
    const key = `job:${jobId}`
    const jobDataStr = await kv.get(key)
    return jobDataStr ? JSON.parse(jobDataStr) : null
  } catch (error) {
    console.error('KV job status get error:', error)
    return null
  }
}

export async function deleteJobStatus(jobId: string): Promise<void> {
  try {
    const kv = getKVClient()
    const key = `job:${jobId}`
    await kv.delete(key)
  } catch (error) {
    console.error('KV job status delete error:', error)
    throw error
  }
}

// Legacy function names for backward compatibility during migration
export const checkQuotaRedis = checkQuotaKV
export const incrementQuotaRedis = incrementQuotaKV
export const resetQuotaRedis = resetQuotaKV
export const getRedisClient = getKVClient
