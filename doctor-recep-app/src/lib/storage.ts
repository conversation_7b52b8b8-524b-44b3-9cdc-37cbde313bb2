// Cloudflare R2 Bucket type definition
interface R2Bucket {
  put(key: string, value: ArrayBuffer | ArrayBufferView | string | null | ReadableStream, options?: {
    httpMetadata?: {
      contentType?: string
      contentLanguage?: string
      contentDisposition?: string
      contentEncoding?: string
      cacheControl?: string
      expires?: Date
    }
    customMetadata?: Record<string, string>
  }): Promise<R2Object>

  get(key: string, options?: {
    range?: { offset?: number; length?: number; suffix?: number }
    onlyIf?: { etagMatches?: string; etagDoesNotMatch?: string; uploadedBefore?: Date; uploadedAfter?: Date }
  }): Promise<R2ObjectBody | null>

  delete(keys: string | string[]): Promise<void>

  head(key: string): Promise<R2Object | null>

  list(options?: {
    limit?: number
    prefix?: string
    cursor?: string
    delimiter?: string
    startAfter?: string
    include?: ('httpMetadata' | 'customMetadata')[]
  }): Promise<R2Objects>
}

interface R2Object {
  key: string
  version: string
  size: number
  etag: string
  httpEtag: string
  uploaded: Date
  httpMetadata?: {
    contentType?: string
    contentLanguage?: string
    contentDisposition?: string
    contentEncoding?: string
    cacheControl?: string
    expires?: Date
  }
  customMetadata?: Record<string, string>
  range?: { offset: number; length: number }
}

interface R2ObjectBody extends R2Object {
  body: ReadableStream
  bodyUsed: boolean
  arrayBuffer(): Promise<ArrayBuffer>
  text(): Promise<string>
  json<T = unknown>(): Promise<T>
  blob(): Promise<Blob>
}

interface R2Objects {
  objects: R2Object[]
  truncated: boolean
  cursor?: string
  delimitedPrefixes: string[]
}

// Storage configuration - Migrated to Cloudflare R2
export const STORAGE_CONFIG = {
  // R2 Configuration
  BUCKET_NAME: process.env.R2_BUCKET_NAME || 'celerai-storage',
  PUBLIC_URL: process.env.R2_PUBLIC_URL || 'https://celerai.tallyup.pro',

  // Folder prefixes (replaces separate buckets)
  AUDIO_PREFIX: 'consultation-audio',
  IMAGE_PREFIX: 'consultation-images',

  // File limits
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB per file
  MAX_TOTAL_SIZE: 200 * 1024 * 1024, // 200MB per consultation
  ALLOWED_AUDIO_TYPES: ['audio/webm', 'audio/mp3', 'audio/wav', 'audio/m4a', 'audio/mpeg', 'audio/mp4', 'audio/ogg'],
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic'],
  RETENTION_DAYS: 30
}

// Get R2 bucket from Cloudflare Workers binding
function getR2Bucket(): R2Bucket | null {
  // Try multiple ways to access the R2 binding
  try {
    // Method 1: Through Cloudflare context (OpenNext)
    const context = (globalThis as any)[Symbol.for("__cloudflare-context__")];
    if (context?.env?.CELERAI_BUCKET) {
      return context.env.CELERAI_BUCKET;
    }

    // Method 2: Direct global access (fallback)
    if ((globalThis as any).CELERAI_BUCKET) {
      return (globalThis as any).CELERAI_BUCKET;
    }

    // Method 3: Through env object (if available)
    if ((globalThis as any).env?.CELERAI_BUCKET) {
      return (globalThis as any).env.CELERAI_BUCKET;
    }

    return null;
  } catch (error) {
    console.error('Error accessing R2 bucket:', error);
    return null;
  }
}

// File validation
export function validateFile(file: File, type: 'audio' | 'image'): { valid: boolean; error?: string } {
  // Check file size
  if (file.size > STORAGE_CONFIG.MAX_FILE_SIZE) {
    return { valid: false, error: `File size exceeds ${STORAGE_CONFIG.MAX_FILE_SIZE / 1024 / 1024}MB limit` }
  }

  // Check file type
  const allowedTypes = type === 'audio' ? STORAGE_CONFIG.ALLOWED_AUDIO_TYPES : STORAGE_CONFIG.ALLOWED_IMAGE_TYPES
  if (!allowedTypes.includes(file.type)) {
    return { valid: false, error: `File type ${file.type} is not allowed` }
  }

  return { valid: true }
}

// Generate storage path with folder prefix for R2
export function generateStoragePath(
  doctorId: string,
  consultationId: string,
  fileName: string,
  type: 'audio' | 'image'
): string {
  const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_')
  const prefix = type === 'audio' ? STORAGE_CONFIG.AUDIO_PREFIX : STORAGE_CONFIG.IMAGE_PREFIX
  return `${prefix}/${doctorId}/${consultationId}/${sanitizedFileName}`
}

// Upload file to Cloudflare R2
export async function uploadFile(
  file: File,
  doctorId: string,
  consultationId: string,
  type: 'audio' | 'image'
): Promise<{ success: boolean; url?: string; error?: string }> {
  try {
    // Validate file
    const validation = validateFile(file, type)
    if (!validation.valid) {
      return { success: false, error: validation.error }
    }

    const r2Bucket = getR2Bucket()
    if (!r2Bucket) {
      console.error('R2 bucket binding not available. Available globals:', Object.keys(globalThis).filter(k => k.includes('BUCKET') || k.includes('env')));
      return { success: false, error: 'R2 bucket binding not available' }
    }

    const filePath = generateStoragePath(doctorId, consultationId, file.name, type)

    // Convert file to buffer
    const fileBuffer = await file.arrayBuffer()

    // Upload to R2 using direct binding
    await r2Bucket.put(filePath, fileBuffer, {
      httpMetadata: {
        contentType: file.type, // Preserve exact Content-Type from file
        cacheControl: 'public, max-age=3600',
      },
    })

    // Generate public URL
    const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`

    return { success: true, url: publicUrl }
  } catch (error) {
    console.error('R2 upload error:', error)
    return { success: false, error: `Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}` }
  }
}

// Upload multiple files
export async function uploadMultipleFiles(
  files: File[],
  doctorId: string,
  consultationId: string,
  type: 'audio' | 'image'
): Promise<{ success: boolean; urls?: string[]; errors?: string[] }> {
  const results = await Promise.all(
    files.map(file => uploadFile(file, doctorId, consultationId, type))
  )

  const successful = results.filter(r => r.success)
  const failed = results.filter(r => !r.success)

  if (failed.length > 0) {
    return {
      success: false,
      errors: failed.map(f => f.error || 'Unknown error')
    }
  }

  return {
    success: true,
    urls: successful.map(s => s.url!).filter(Boolean)
  }
}

// Delete file from R2 storage
export async function deleteFile(
  filePath: string,
  _type: 'audio' | 'image'
): Promise<{ success: boolean; error?: string }> {
  try {
    const r2Bucket = getR2Bucket()
    if (!r2Bucket) {
      return { success: false, error: 'R2 bucket binding not available' }
    }

    await r2Bucket.delete(filePath)

    return { success: true }
  } catch (error) {
    console.error('R2 delete error:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Delete failed' }
  }
}

// Extract file path from R2 URL
export function extractFilePathFromUrl(url: string, type: 'audio' | 'image'): string | null {
  try {
    const prefix = type === 'audio' ? STORAGE_CONFIG.AUDIO_PREFIX : STORAGE_CONFIG.IMAGE_PREFIX
    const prefixPath = `/${prefix}/`
    const index = url.indexOf(prefixPath)

    if (index === -1) return null

    return url.substring(url.indexOf(prefix))
  } catch {
    return null
  }
}

// Download file from R2 storage
export async function downloadFile(
  filePath: string,
  _type: 'audio' | 'image'
): Promise<{ success: boolean; data?: Blob; error?: string }> {
  try {
    // For public files, we can fetch directly from the custom domain
    const publicUrl = `${STORAGE_CONFIG.PUBLIC_URL}/${filePath}`

    const response = await fetch(publicUrl)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.blob()
    return { success: true, data }
  } catch (error) {
    console.error('R2 download error:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Download failed' }
  }
}

// Calculate total file size
export function calculateTotalFileSize(files: File[]): number {
  return files.reduce((total, file) => total + file.size, 0)
}

// Validate total consultation file size
export function validateTotalSize(files: File[]): { valid: boolean; error?: string } {
  const totalSize = calculateTotalFileSize(files)
  if (totalSize > STORAGE_CONFIG.MAX_TOTAL_SIZE) {
    return {
      valid: false,
      error: `Total file size exceeds ${STORAGE_CONFIG.MAX_TOTAL_SIZE / 1024 / 1024}MB limit`
    }
  }
  return { valid: true }
}
