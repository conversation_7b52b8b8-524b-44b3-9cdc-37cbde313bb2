'use server'

import { createClient } from '@/lib/supabase/server'
import { isDoctorProfile } from '@/lib/guards'

/**
 * Increment quota usage in the database (async background operation)
 * This is called after the KV increment to sync the database
 * Uses type guards for safe access to doctor-specific fields
 */
export async function incrementQuotaUsage(userId: string): Promise<void> {
  try {
    const supabase = await createClient()

    // Get current profile and verify it's a doctor
    const { data: profile, error: fetchError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (fetchError || !profile) {
      console.error('Failed to fetch profile for quota update:', fetchError)
      return
    }

    // Use type guard to ensure safe access to quota fields
    if (!isDoctorProfile(profile)) {
      console.error('Cannot increment quota for non-doctor profile:', userId)
      return
    }

    // Increment quota usage
    const { error } = await supabase
      .from('profiles')
      .update({
        quota_used: profile.quota_used + 1,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId)

    if (error) {
      console.error('Database quota update error:', error)
      // Don't throw - this is a background operation
    }
  } catch (error) {
    console.error('Quota usage increment error:', error)
    // Don't throw - this is a background operation
  }
}

/**
 * Sync Redis quota with database quota
 * This can be called periodically or when needed
 * Uses type guards for safe access to doctor-specific fields
 */
export async function syncQuotaWithDatabase(userId: string): Promise<void> {
  try {
    const supabase = await createClient()

    const { data: profile, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (error || !profile) {
      console.error('Failed to fetch quota from database:', error)
      return
    }

    // Use type guard to ensure safe access to quota fields
    if (!isDoctorProfile(profile)) {
      console.error('Cannot sync quota for non-doctor profile:', userId)
      return
    }

    // Sync with KV
    const { syncQuotaFromDatabase } = await import('@/lib/kv')
    await syncQuotaFromDatabase(userId, profile.quota_used)
  } catch (error) {
    console.error('Quota sync error:', error)
  }
}
