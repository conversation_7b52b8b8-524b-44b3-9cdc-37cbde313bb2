# 🚀 Cloudflare Workers Deployment Guide

## **TL;DR - Quick Deployment**

```bash
# 1. Make script executable
chmod +x deploy-cloudflare.sh

# 2. Run the deployment script
./deploy-cloudflare.sh
```

That's it! The script handles everything automatically.

---

## **What the Script Does Automatically**

### ✅ **No Manual Worker Creation Needed**
- Wrangler CLI creates the worker automatically
- No need to touch the Cloudflare dashboard initially
- Everything is done via command line

### ✅ **Complete Automation**
1. **Checks Prerequisites:**
   - Installs Wrangler CLI if missing
   - Handles Cloudflare authentication
   
2. **Builds Application:**
   - Runs `npm run build:cf` (OpenNext build)
   - Prepares all assets for Workers

3. **Sets All Environment Variables:**
   - All 20+ secrets from your `.env.local`
   - Uses `wrangler secret put` for each one
   - No manual dashboard work needed

4. **Deploys Worker:**
   - Creates worker named `celer-ai-app-prod`
   - Deploys to production environment
   - Gives you the worker URL

---

## **Step-by-Step Breakdown**

### **Step 1: Prerequisites**
```bash
# Install Node.js dependencies (if not done)
npm install

# The script will handle Wrangler installation and login
```

### **Step 2: Authentication**
When you run the script, if you're not logged in to Cloudflare:
```bash
# The script will prompt you to login
wrangler login
# This opens browser for Cloudflare OAuth
```

### **Step 3: Automatic Deployment**
```bash
# Run the deployment script
./deploy-cloudflare.sh
```

The script will:
- ✅ Build your app with OpenNext
- ✅ Set all 20+ environment variables as secrets
- ✅ Create and deploy the worker
- ✅ Give you the deployment URL

---

## **Expected Output**

```bash
🚀 Starting Cloudflare Workers deployment for Celer AI...
🔐 Checking Cloudflare authentication...
✅ Cloudflare authentication verified
🏗️  Building application for Cloudflare Workers...
🔐 Setting up environment variables and secrets...
📊 Setting up Supabase configuration...
🔐 Setting up authentication and security...
📱 Setting up SMS service...
📧 Setting up email service...
🗄️  Setting up Redis...
☁️  Setting up R2 storage...
🤖 Setting up external APIs...
📝 Setting up CMS...
📊 Setting up monitoring...
✅ All secrets configured successfully!
🚀 Deploying to Cloudflare Workers...
🎉 Deployment completed successfully!

📋 Deployment Summary:
   • Worker Name: celer-ai-app-prod
   • Environment: production
   • URL: https://celer-ai-app-prod.your-subdomain.workers.dev

🔧 Next Steps:
   1. Configure custom domain worker.celerai.live in Cloudflare dashboard
   2. Test the deployment at the worker URL
   3. Update DNS to point worker.celerai.live to the worker

✅ Celer AI is now running on Cloudflare Workers!
```

---

## **After Deployment**

### **1. Test the Worker**
```bash
# Test basic functionality
curl https://celer-ai-app-prod.your-subdomain.workers.dev

# Test API endpoint
curl https://celer-ai-app-prod.your-subdomain.workers.dev/api/health
```

### **2. Set Up Custom Domain (worker.celerai.live)**
1. Go to Cloudflare Dashboard
2. Navigate to Workers & Pages
3. Click on your worker `celer-ai-app-prod`
4. Go to Settings > Triggers
5. Add custom domain: `worker.celerai.live`

### **3. Update DNS**
- Point `worker.celerai.live` to your worker
- Cloudflare will handle this automatically when you add the custom domain

---

## **Manual Alternative (If Script Fails)**

If the script doesn't work for any reason:

```bash
# 1. Build
npm run build:cf

# 2. Login to Cloudflare
wrangler login

# 3. Set secrets manually (example)
wrangler secret put NEXT_PUBLIC_SUPABASE_URL --env production
# Enter: https://edojplwmwtytpdssrxbh.supabase.co

# 4. Deploy
wrangler deploy --env production
```

---

## **Key Points**

✅ **No manual worker creation** - Wrangler does it all
✅ **No dashboard work initially** - Everything via CLI
✅ **All secrets set automatically** - From your .env.local values
✅ **Production-ready deployment** - Uses production environment
✅ **Custom domain ready** - Just needs DNS configuration

The script is designed to be **completely hands-off** - just run it and your app will be live on Cloudflare Workers!
