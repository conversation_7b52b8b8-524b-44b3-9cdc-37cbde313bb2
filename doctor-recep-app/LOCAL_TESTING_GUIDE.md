# 🧪 Local Cloudflare Workers Testing Guide

## **Step 1: Local Testing First**

Before deploying to production, let's test everything locally to make sure it works perfectly.

### **🚀 Quick Local Test**
```bash
# 1. Make script executable
chmod +x test-local-worker.sh

# 2. Run local testing
./test-local-worker.sh
```

---

## **What the Local Test Does**

### ✅ **Builds Your App for Workers**
- Runs `npm run build:cf` (OpenNext build)
- Creates `.open-next/` directory with worker-compatible files
- Verifies build completes successfully

### ✅ **Sets Up Local Environment**
- Creates `.dev.vars` file with all your secrets
- Uses the same values from your `.env.local`
- No need to set secrets in Cloudflare yet

### ✅ **Starts Local Worker Server**
- Runs on `http://localhost:8787`
- Simulates exact Cloudflare Workers environment
- All your APIs and pages work locally

---

## **Expected Local Test Output**

```bash
🧪 Starting local Cloudflare Workers testing for Celer AI...
✅ Wrangler CLI found
🏗️  Building application for Cloudflare Workers...
✅ Build completed successfully
🔐 Setting up local environment variables...
✅ Local environment variables configured
🚀 Starting local Cloudflare Workers development server...

📋 Local Testing Information:
   • Local URL: http://localhost:8787
   • Environment: development
   • All secrets loaded from .dev.vars

🧪 Test these endpoints:
   • Homepage: http://localhost:8787
   • API Health: http://localhost:8787/api/health (if exists)
   • Dashboard: http://localhost:8787/dashboard
   • Admin: http://localhost:8787/admin

✅ Press Ctrl+C to stop the local server
```

---

## **🧪 What to Test Locally**

### **1. Basic Functionality**
- ✅ Homepage loads (`http://localhost:8787`)
- ✅ Authentication pages work
- ✅ Dashboard loads (if logged in)
- ✅ Admin panel works

### **2. API Endpoints**
- ✅ Authentication APIs (`/api/auth/*`)
- ✅ File upload APIs (`/api/audio-proxy`)
- ✅ Database operations work
- ✅ Redis connections work

### **3. External Services**
- ✅ Supabase connection
- ✅ Redis (Upstash) connection
- ✅ R2 storage access
- ✅ SMS service (VerifyNow)
- ✅ Email service (Resend)

### **4. Edge Runtime Compatibility**
- ✅ All server actions work
- ✅ Middleware functions properly
- ✅ No Node.js compatibility issues

---

## **🔍 Troubleshooting Local Issues**

### **Build Fails**
```bash
# Check for missing dependencies
npm install

# Check for TypeScript errors
npm run lint

# Check specific build output
npm run build:cf
```

### **Environment Variables Missing**
```bash
# Check .dev.vars file was created
cat .dev.vars

# Verify all required variables are present
grep -c "=" .dev.vars  # Should show 20+ variables
```

### **Server Won't Start**
```bash
# Check if port 8787 is available
lsof -i :8787

# Try different port
wrangler dev --local --port 8788
```

### **API Errors**
- Check browser console for errors
- Check terminal output for server errors
- Verify external services (Supabase, Redis) are accessible

---

## **✅ When Local Testing Passes**

Once everything works locally:

1. **Stop the local server** (Ctrl+C)
2. **Run the production deployment:**
   ```bash
   ./deploy-cloudflare.sh
   ```

---

## **🔄 Local vs Production Differences**

### **Local Environment:**
- Uses `.dev.vars` file for secrets
- Runs on `localhost:8787`
- Development mode
- Hot reloading available

### **Production Environment:**
- Uses Cloudflare secrets
- Runs on `worker.celerai.live`
- Production mode
- Optimized performance

---

## **📋 Local Testing Checklist**

- [ ] Build completes without errors
- [ ] Local server starts successfully
- [ ] Homepage loads at `localhost:8787`
- [ ] Authentication flow works
- [ ] Database queries work (Supabase)
- [ ] File uploads work (R2)
- [ ] Redis operations work (Upstash)
- [ ] External APIs respond (SMS, Email)
- [ ] No console errors in browser
- [ ] All critical user flows work

**Once all items are checked ✅, you're ready for production deployment!**

---

## **🚀 Next Step: Production Deployment**

After successful local testing:
```bash
# Deploy to production
./deploy-cloudflare.sh
```

This will deploy to Cloudflare Workers with the same configuration that worked locally.
