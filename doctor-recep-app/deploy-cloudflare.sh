#!/bin/bash

# Cloudflare Workers Deployment Script for Celer AI
# This script handles the complete deployment process

set -e  # Exit on any error

echo "🚀 Starting Cloudflare Workers deployment for Celer AI..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if wrangler is installed
if ! command -v wrangler &> /dev/null; then
    echo -e "${RED}❌ Wrangler CLI not found. Installing...${NC}"
    npm install -g wrangler
fi

# Check if user is logged in to Cloudflare
echo -e "${BLUE}🔐 Checking Cloudflare authentication...${NC}"
if ! wrangler whoami &> /dev/null; then
    echo -e "${YELLOW}⚠️  Not logged in to Cloudflare. Please log in:${NC}"
    wrangler login
fi

echo -e "${GREEN}✅ Cloudflare authentication verified${NC}"

# Build the application for Cloudflare Workers
echo -e "${BLUE}🏗️  Building application for Cloudflare Workers...${NC}"
npm run build:cf

# Set up all environment variables/secrets
echo -e "${BLUE}🔐 Setting up environment variables and secrets...${NC}"

# Function to set secret with error handling
set_secret() {
    local key=$1
    local value=$2
    echo -e "${YELLOW}Setting secret: $key${NC}"
    echo "$value" | wrangler secret put "$key" --env production
}

# Supabase Configuration
echo -e "${BLUE}📊 Setting up Supabase configuration...${NC}"
set_secret "NEXT_PUBLIC_SUPABASE_URL" "https://edojplwmwtytpdssrxbh.supabase.co"
set_secret "NEXT_PUBLIC_SUPABASE_ANON_KEY" "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVkb2pwbHdtd3R5dHBkc3NyeGJoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDY4MjcsImV4cCI6MjA2Njg4MjgyN30.dnzk7za0YQ-5R9Ses5r1DFNw74nWzCMoPeK0uXRoEyQ"
set_secret "SUPABASE_SERVICE_ROLE_KEY" "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVkb2pwbHdtd3R5dHBkc3NyeGJoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwNjgyNywiZXhwIjoyMDY2ODgyODI3fQ.V_dz0QJMHEnBWqF-7XfrYqHIfZ_IRwNS6cuMH_0iOTQ"

# Authentication & Security
echo -e "${BLUE}🔐 Setting up authentication and security...${NC}"
set_secret "SESSION_SECRET" "dbc4e13fd8fd3a091808cbe92d09f692a69fed648c257c2774fc9a7990f25fc6"
set_secret "WEBHOOK_SECRET_TOKEN" "53651324f67a155e09e6b689fbba728e1a06aae9bfc5a98c8d54f0108883f67a"
set_secret "INTERNAL_API_TOKEN" "f6f5ee49be16ae3b8fcea657fdad2f718b7906061b483b68c89619b176e31772"

# SMS Service (VerifyNow)
echo -e "${BLUE}📱 Setting up SMS service...${NC}"
set_secret "VERIFYNOW_AUTH_TOKEN" "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJDLTdBRTI1Mjc2RkM5MjRDRSIsImlhdCI6MTc1MTIwMjE3OCwiZXhwIjoxOTA4ODgyMTc4fQ.xDLDKjEDTcH8MsTW714QVVlHKAp_eOP_hvj3ZBHFGgL4SMnIIXl2-4fKbH3VsKav2BVpAo-RdprXdaGVdovePA"
set_secret "VERIFYNOW_CUSTOMER_ID" "C-7AE25276FC924CE"

# Email Service
echo -e "${BLUE}📧 Setting up email service...${NC}"
set_secret "RESEND_API_KEY" "re_NMxerRQ8_NVFs1nFu9MFGC5QsGviAXjaA"

# Redis credentials no longer needed - migrated to Cloudflare KV
# echo -e "${BLUE}🗄️  Redis setup skipped - using Cloudflare KV${NC}"

# Cloudflare R2 Storage
echo -e "${BLUE}☁️  Setting up R2 storage...${NC}"
set_secret "R2_ACCESS_KEY_ID" "4dff08f96bf2f040b48bf3973813f7f0"
set_secret "R2_SECRET_ACCESS_KEY" "6fb6d9a306ab8df2626e7519440424ed92ecf756d7c98514ff801c17268f9856"
set_secret "R2_ACCOUNT_ID" "57014886c6cd87ebacf23a94e56a6e0c"
set_secret "R2_BUCKET_NAME" "celerai-storage"
set_secret "R2_PUBLIC_URL" "https://celerai.tallyup.pro"

# External APIs
echo -e "${BLUE}🤖 Setting up external APIs...${NC}"
set_secret "GEMINI_API_KEY" "AIzaSyCuRoRkbCzzu9VDYfdgTj6rSWC_AKtUb94"
set_secret "NEXT_PUBLIC_API_URL" "https://doctor-recep-api-************.asia-south1.run.app"

# CMS (Sanity)
echo -e "${BLUE}📝 Setting up CMS...${NC}"
set_secret "NEXT_PUBLIC_SANITY_PROJECT_ID" "xpo3opql"
set_secret "NEXT_PUBLIC_SANITY_DATASET" "production"
set_secret "NEXT_PUBLIC_SANITY_TOKEN" "skNzZ3GJWN8nlU40kNfgrBFwJ5HGr2xVs5azIgr9l3JueGVgOLNVwiN6hUlVK6STGcW5OQWWwCMnE7nl7G0SQKeEUnZnDdRnDqhItLErtuBa5gkkduOqh5hsfnBGAVjNt65TnVcvFugvcY5jgLs68XaQbOx82hKp6lLRPwJLNHLnQ0lnPHBE"

echo -e "${GREEN}✅ All secrets configured successfully!${NC}"

# Deploy to Cloudflare Workers
echo -e "${BLUE}🚀 Deploying to Cloudflare Workers...${NC}"
wrangler deploy --env production

# Get the deployment URL
echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo -e "${BLUE}📋 Deployment Summary:${NC}"
echo -e "   • Worker Name: celer-ai-app-prod"
echo -e "   • Environment: production"
echo -e "   • URL: https://celer-ai-app-prod.your-subdomain.workers.dev"
echo ""
echo -e "${YELLOW}🔧 Next Steps:${NC}"
echo -e "   1. Configure custom domain worker.celerai.live in Cloudflare dashboard"
echo -e "   2. Test the deployment at the worker URL"
echo -e "   3. Update DNS to point worker.celerai.live to the worker"
echo ""
echo -e "${GREEN}✅ Celer AI is now running on Cloudflare Workers!${NC}"
