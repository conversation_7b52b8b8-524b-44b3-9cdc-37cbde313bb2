/**
 * KV Migration Test
 * 
 * This test verifies that the Redis to Cloudflare KV migration is working correctly.
 * Run this test in a Cloudflare Workers environment using wrangler dev.
 */

// Mock KV for testing purposes
class MockKV {
  constructor() {
    this.store = new Map()
    this.ttls = new Map()
  }

  async get(key) {
    // Check if key has expired
    const ttl = this.ttls.get(key)
    if (ttl && Date.now() > ttl) {
      this.store.delete(key)
      this.ttls.delete(key)
      return null
    }
    return this.store.get(key) || null
  }

  async put(key, value, options = {}) {
    this.store.set(key, value)
    if (options.expirationTtl) {
      this.ttls.set(key, Date.now() + (options.expirationTtl * 1000))
    }
  }

  async delete(key) {
    this.store.delete(key)
    this.ttls.delete(key)
  }

  async list() {
    return { keys: Array.from(this.store.keys()).map(name => ({ name })) }
  }
}

// Test quota management functions
async function testQuotaManagement() {
  console.log('🧪 Testing Quota Management...')
  
  // Mock the KV binding
  globalThis.CELERAI_KV = new MockKV()
  
  // Import the KV functions
  const { checkQuotaKV, incrementQuotaKV, resetQuotaKV, syncQuotaFromDatabase } = await import('../src/lib/kv.ts')
  
  const testUserId = 'test-user-123'
  
  try {
    // Test 1: Check initial quota (should be 0)
    console.log('  ✓ Testing initial quota check...')
    const initialCheck = await checkQuotaKV(testUserId)
    console.assert(initialCheck.currentUsage === 0, 'Initial usage should be 0')
    console.assert(initialCheck.allowed === true, 'Initial request should be allowed')
    
    // Test 2: Increment quota
    console.log('  ✓ Testing quota increment...')
    const newUsage = await incrementQuotaKV(testUserId)
    console.assert(newUsage === 1, 'Usage should be 1 after first increment')
    
    // Test 3: Check quota after increment
    console.log('  ✓ Testing quota check after increment...')
    const afterIncrement = await checkQuotaKV(testUserId)
    console.assert(afterIncrement.currentUsage === 1, 'Usage should be 1')
    console.assert(afterIncrement.allowed === true, 'Request should still be allowed')
    
    // Test 4: Multiple increments
    console.log('  ✓ Testing multiple increments...')
    for (let i = 2; i <= 50; i++) {
      await incrementQuotaKV(testUserId)
    }
    
    const atLimit = await checkQuotaKV(testUserId)
    console.assert(atLimit.currentUsage === 50, 'Usage should be 50')
    console.assert(atLimit.allowed === false, 'Request should be denied at limit')
    
    // Test 5: Reset quota
    console.log('  ✓ Testing quota reset...')
    await resetQuotaKV(testUserId)
    const afterReset = await checkQuotaKV(testUserId)
    console.assert(afterReset.currentUsage === 0, 'Usage should be 0 after reset')
    console.assert(afterReset.allowed === true, 'Request should be allowed after reset')
    
    // Test 6: Sync from database
    console.log('  ✓ Testing database sync...')
    await syncQuotaFromDatabase(testUserId, 25)
    const afterSync = await checkQuotaKV(testUserId)
    console.assert(afterSync.currentUsage === 25, 'Usage should be 25 after sync')
    
    console.log('✅ Quota Management tests passed!')
    
  } catch (error) {
    console.error('❌ Quota Management test failed:', error)
    throw error
  }
}

// Test job status tracking functions
async function testJobStatusTracking() {
  console.log('🧪 Testing Job Status Tracking...')
  
  // Import the KV functions
  const { setJobStatus, getJobStatus, deleteJobStatus } = await import('../src/lib/kv.ts')
  
  const testJobId = 'test-job-456'
  const testJobData = {
    jobId: testJobId,
    userId: 'test-user-123',
    type: 'ai_generation',
    status: 'pending',
    createdAt: new Date().toISOString(),
    metadata: { test: true }
  }
  
  try {
    // Test 1: Set job status
    console.log('  ✓ Testing job status set...')
    await setJobStatus(testJobId, testJobData, 1800)
    
    // Test 2: Get job status
    console.log('  ✓ Testing job status get...')
    const retrievedJob = await getJobStatus(testJobId)
    console.assert(retrievedJob !== null, 'Job should exist')
    console.assert(retrievedJob.jobId === testJobId, 'Job ID should match')
    console.assert(retrievedJob.status === 'pending', 'Status should be pending')
    
    // Test 3: Update job status
    console.log('  ✓ Testing job status update...')
    const updatedJobData = { ...testJobData, status: 'completed', result: 'success' }
    await setJobStatus(testJobId, updatedJobData, 3600)
    
    const updatedJob = await getJobStatus(testJobId)
    console.assert(updatedJob.status === 'completed', 'Status should be completed')
    console.assert(updatedJob.result === 'success', 'Result should be success')
    
    // Test 4: Delete job status
    console.log('  ✓ Testing job status delete...')
    await deleteJobStatus(testJobId)
    const deletedJob = await getJobStatus(testJobId)
    console.assert(deletedJob === null, 'Job should be deleted')
    
    console.log('✅ Job Status Tracking tests passed!')
    
  } catch (error) {
    console.error('❌ Job Status Tracking test failed:', error)
    throw error
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting KV Migration Tests...\n')
  
  try {
    await testQuotaManagement()
    console.log('')
    await testJobStatusTracking()
    
    console.log('\n🎉 All KV Migration tests passed!')
    console.log('✅ Redis to Cloudflare KV migration is working correctly!')
    
  } catch (error) {
    console.error('\n💥 KV Migration tests failed!')
    console.error('❌ Migration verification failed:', error)
    process.exit(1)
  }
}

// Run tests if this file is executed directly
if (typeof module !== 'undefined' && require.main === module) {
  runTests()
}

module.exports = { runTests, testQuotaManagement, testJobStatusTracking }
