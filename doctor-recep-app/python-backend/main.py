"""
Celer AI System - Python Backend
Using FastAPI and Google Gemini 2.5 Flash Preview with Base64 Inline Data
"""

import os
import logging
import mimetypes
import asyncio
from typing import List, Optional, Tuple, Any, Dict # Added Dict for prompt config type
# ### ADDED: Import Request to access app state in handlers
from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
# ### ADDED: Import ValidationError for robust error handling
from pydantic import BaseModel, ValidationError
from dotenv import load_dotenv
import httpx
import tempfile
# import aiofiles # Removed as it's not used in updated script
from datetime import datetime
from urllib.parse import urlparse
# import ffmpeg  # Using subprocess directly instead
import io
from PIL import Image
from google import genai
from google.genai import types # Correctly placed at top level
import json # Added for streaming response
from contextlib import asynccontextmanager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# ### ADDED: Pydantic models for validating the prompts.json configuration
class PromptTemplate(BaseModel):
    format: str
    style: str
    ai_instruction: str
    template_structure: str
    instructions: str
    sections: List[str]

class PromptConfig(BaseModel):
    outpatient: PromptTemplate
    discharge: PromptTemplate
    surgery: PromptTemplate
    radiology: PromptTemplate
    dermatology: PromptTemplate
    cardiology_echo: PromptTemplate
    ivf_cycle: PromptTemplate
    pathology: PromptTemplate

# ### ADDED: Function to load and validate the configuration file
def load_prompt_config(path: str) -> Optional[PromptConfig]:
    """Loads and validates the prompt configuration from a JSON file."""
    try:
        with open(path, 'r') as f:
            data = json.load(f)
        config = PromptConfig.model_validate(data)
        logger.info(f"✅ Successfully loaded and validated prompt configuration from {path}")
        return config
    except FileNotFoundError:
        logger.error(f"❌ CONFIGURATION ERROR: The prompt file '{path}' was not found.")
        return None
    except ValidationError as e:
        logger.error(f"❌ CONFIGURATION ERROR: The prompt file '{path}' has invalid data. Details: {e}")
        return None
    except Exception as e:
        logger.error(f"❌ CONFIGURATION ERROR: An unexpected error occurred while loading '{path}'. Details: {e}")
        return None

# Lifespan event handler
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("🚀 Application starting up...")
    # ### MODIFIED: Load prompt configuration on startup
    app.state.prompt_config = load_prompt_config("prompts.json")
    if not app.state.prompt_config:
        # This will prevent the app from running in a broken state
        raise RuntimeError("Failed to load prompt configuration. Application cannot start.")

    yield
    # Shutdown (if needed)
    pass

# Initialize FastAPI app
app = FastAPI(
    title="Celer AI API",
    description="AI-powered consultation summary system using Gemini 2.5 Flash Preview with Base64 Inline Data",
    version="2.0.0",
    lifespan=lifespan
)

# Configure CORS - supports single or multiple frontend URLs
frontend_urls_str = os.getenv("FRONTEND_URLS", os.getenv("FRONTEND_URL", "http://localhost:3004"))
frontend_urls = [url.strip() for url in frontend_urls_str.split(',') if url.strip()]

app.add_middleware(
    CORSMiddleware,
    allow_origins=frontend_urls,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Context manager for Gemini client
class GeminiClientManager:
    """Context manager for Gemini client"""
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.client = None

    async def __aenter__(self):
        try:
            self.client = genai.Client(api_key=self.api_key)
            logger.info("✅ Gemini client initialized successfully")
            return self.client
        except Exception as e:
            logger.error(f"❌ Failed to initialize Gemini client: {e}")
            raise

    async def __aexit__(self, exc_type, exc_value, traceback):
        if self.client:
            logger.info("✅ Gemini client manager exiting.")


# Initialize Gemini client manager
gemini_manager = GeminiClientManager(api_key=os.getenv("GEMINI_API_KEY"))

# Pydantic models

# ### REMOVED: The hardcoded CONSULTATION_TEMPLATES dictionary is no longer needed.
# CONSULTATION_TEMPLATES = { ... }

class GenerateSummaryRequest(BaseModel):
    primary_audio_url: str
    additional_audio_urls: Optional[List[str]] = []
    image_urls: Optional[List[str]] = []
    submitted_by: str = "doctor"
    consultation_type: str = "outpatient"
    doctor_notes: Optional[str] = None
    additional_notes: Optional[str] = None
    patient_name: Optional[str] = None
    doctor_name: Optional[str] = None
    created_at: Optional[str] = None



class GenerateSummaryStreamRequest(BaseModel): # Preserved from original, identical to updated
    primary_audio_url: str
    additional_audio_urls: Optional[List[str]] = []
    image_urls: Optional[List[str]] = []
    submitted_by: str = "doctor"
    consultation_type: str = "outpatient"
    doctor_notes: Optional[str] = None
    additional_notes: Optional[str] = None
    patient_name: Optional[str] = None
    doctor_name: Optional[str] = None
    created_at: Optional[str] = None

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    client_connected = False
    try:
        temp_client = genai.Client(api_key=gemini_manager.api_key)
        if temp_client:
            client_connected = True
    except Exception:
        client_connected = False

    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "gemini_client": "connected" if client_connected else "disconnected (initialization check)",
        "model": "gemini-2.5-flash-preview-05-20" # Aligned with spec
    }

# ### MODIFIED: generate_prompt now accepts the loaded config as an argument
def generate_prompt(
    prompt_templates: Dict[str, PromptTemplate],
    submitted_by: str,
    consultation_type: str = "outpatient",
    doctor_notes: Optional[str] = None,
    additional_notes: Optional[str] = None,
    patient_name: Optional[str] = None,
    doctor_name: Optional[str] = None,
    created_at: Optional[str] = None
) -> str:
    """Generate AI prompt based on template configuration and consultation type"""
    context_note = (
        "This consultation was recorded by the doctor during patient visit."
        if submitted_by == "doctor"
        else "This consultation is being reviewed by the receptionist for final summary."
    )

    # ### MODIFIED: Get template from the passed-in dictionary
    logger.info(f"🔍 GENERATE_PROMPT DEBUG: Available templates: {list(prompt_templates.keys())}")
    logger.info(f"🔍 GENERATE_PROMPT DEBUG: Requested consultation_type: {consultation_type}")
    logger.info(f"🔍 GENERATE_PROMPT DEBUG: Template exists: {consultation_type in prompt_templates}")

    consultation_template_model = prompt_templates.get(consultation_type, prompt_templates["outpatient"])
    logger.info(f"🔍 GENERATE_PROMPT DEBUG: Selected template type: {type(consultation_template_model)}")

    # Convert Pydantic model back to dict for attribute access, or access attributes directly
    consultation_template = consultation_template_model.model_dump()
    logger.info(f"🔍 GENERATE_PROMPT DEBUG: Template format: {consultation_template.get('format', 'MISSING')}")
    logger.info(f"🔍 GENERATE_PROMPT DEBUG: Template style: {consultation_template.get('style', 'MISSING')}")
    
    sections_text = ", ".join(consultation_template["sections"])
    
    text_sources = []
    if doctor_notes:
        text_sources.append(f"Doctor's Notes: {doctor_notes}")
    if additional_notes:
        text_sources.append(f"Additional Notes: {additional_notes}")
    
    text_context = ""
    if text_sources:
        text_context = f"""

**TEXT SOURCES PROVIDED:**
{chr(10).join(text_sources)}

IMPORTANT: Give equal priority to the text sources above and the audio recordings. Both contain valuable medical information that should be integrated into the final summary.
"""

    ai_instruction = consultation_template["ai_instruction"]
    format_structure = consultation_template["template_structure"]

    # Add patient name, doctor name, and date context if provided
    patient_context = ""
    if patient_name:
        patient_context = f"\nPatient Name: {patient_name}"

    doctor_context = ""
    if doctor_name:
        doctor_context = f"\nDoctor Name: {doctor_name}"

    date_context = ""
    if created_at:
        # Parse and format the date for better readability, convert to IST
        try:
            from datetime import datetime, timezone, timedelta
            # Parse UTC timestamp
            parsed_date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            # Convert to IST (UTC+5:30)
            ist_timezone = timezone(timedelta(hours=5, minutes=30))
            ist_date = parsed_date.astimezone(ist_timezone)
            formatted_date = ist_date.strftime("%B %d, %Y at %I:%M %p IST")
            date_context = f"\nConsultation Date: {formatted_date}"
        except:
            date_context = f"\nConsultation Date: {created_at}"

    return f"""
{ai_instruction}

Context: {context_note}{patient_context}{doctor_context}{date_context}
Consultation Type: {consultation_type.upper()}
Format Style: {consultation_template["style"]}

{consultation_template["instructions"]}

**OUTPUT FORMAT:**
{format_structure}

CRITICAL ACCURACY REQUIREMENTS:
- Only include information explicitly mentioned in audio recordings or provided text notes
- **HIGHLIGHT ANY AMBIGUITIES**: Use **bold text** or mention uncertainty for any unclear medical terms, dosages, or instructions
- Preserve exact medication names and dosages as stated
- Flag any incomplete or unclear information with clear indicators
- Do not add assumptions, differential diagnoses, or recommendations not explicitly stated{text_context}

Requirements:
- Language: English
- Tone: Professional
- Format: {consultation_template["format"]}
- Include sections: {sections_text}

Processing Instructions:
1. **PRIMARY SOURCES**: Process audio recording(s) and any provided text notes with equal priority
2. Extract key medical information from all sources (audio + text)
3. **SECONDARY**: If images are provided, analyze them for visual findings (handwritten notes, prescriptions, medical images)
4. Integrate information from ALL sources (audio + text + images) for complete context
5. Keep summary factual and based only on provided information
6. Use appropriate medical terminology for Indian healthcare context
7. **ACCURACY CHECK**: Highlight any unclear medication names, dosages, or instructions with **bold text**
8. If any information seems incomplete or ambiguous, clearly indicate this in the summary
9. For {consultation_type} consultations: {consultation_template["instructions"]}

Please provide a comprehensive {consultation_template["format"]} based on all provided sources (audio, text, and images). Ensure medical accuracy and highlight any uncertainties clearly.
    """.strip()

def get_file_extension_from_url(url: str) -> str:
    """Extract file extension from URL"""
    parsed_url = urlparse(url)
    path = parsed_url.path
    if '.' in path:
        return path.split('.')[-1].lower()
    return ""

def get_mime_type_from_content_type(content_type: str) -> str:
    """Extract MIME type from content-type header"""
    if content_type:
        return content_type.split(';')[0].strip()
    return ""

async def detect_mime_type(url: str, content_type: str) -> str:
    """Detect MIME type from URL and content-type header"""
    if content_type:
        mime_type = get_mime_type_from_content_type(content_type)
        if mime_type:
            return mime_type
    
    url_extension = get_file_extension_from_url(url)
    if url_extension:
        guessed_type_tuple = await asyncio.to_thread(mimetypes.guess_type, f"file.{url_extension}")
        guessed_type = guessed_type_tuple[0] if guessed_type_tuple else None
        if guessed_type:
            return guessed_type
        
        extension_map = {
            'mp3': 'audio/mpeg', 'wav': 'audio/wav', 'm4a': 'audio/mp4',
            'webm': 'audio/webm', 'ogg': 'audio/ogg', 'aac': 'audio/aac',
            'flac': 'audio/flac', 'jpg': 'image/jpeg', 'jpeg': 'image/jpeg',
            'png': 'image/png', 'webp': 'image/webp', 'heic': 'image/heic'
        }
        return extension_map.get(url_extension, 'application/octet-stream')
    
    return 'application/octet-stream'

async def download_file_from_url(url: str) -> tuple[bytes, str]:
    """Download file from URL and return bytes with detected MIME type"""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client_http:
            response = await client_http.get(url)
            response.raise_for_status()
            content_type = response.headers.get("content-type", "")
            mime_type = await detect_mime_type(url, content_type)
            logger.info(f"📥 Downloaded file: {url} (MIME: {mime_type}, Size: {len(response.content)} bytes)")
            return response.content, mime_type
    except Exception as e:
        logger.error(f"Failed to download file from {url}: {e}")
        raise HTTPException(status_code=400, detail=f"Failed to download file from {url}: {str(e)}")

async def convert_audio_to_wav(file_bytes: bytes) -> tuple[bytes, str]:
    """Convert audio to WAV format with 16kHz mono"""
    def _blocking_ffmpeg_operations():
        tmp_in_name, tmp_out_name = None, None
        try:
            with tempfile.NamedTemporaryFile(suffix='.tmp', delete=False) as tmp_in:
                tmp_in_name = tmp_in.name
                tmp_in.write(file_bytes)
                tmp_in.flush()

            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_out:
                tmp_out_name = tmp_out.name

            # Set FFmpeg executable path explicitly
            import subprocess
            import shutil

            # Find ffmpeg executable
            ffmpeg_path = shutil.which('ffmpeg')
            if not ffmpeg_path:
                # Try common locations
                for path in ['/opt/homebrew/bin/ffmpeg', '/usr/local/bin/ffmpeg', '/usr/bin/ffmpeg']:
                    if os.path.exists(path):
                        ffmpeg_path = path
                        break

            if not ffmpeg_path:
                raise RuntimeError("FFmpeg executable not found")

            # Use subprocess directly instead of ffmpeg-python for better control
            cmd = [
                ffmpeg_path,
                '-i', tmp_in_name,
                '-acodec', 'pcm_s16le',
                '-ac', '1',
                '-ar', '16000',
                '-y',  # overwrite output
                tmp_out_name
            ]

            process = subprocess.run(cmd, capture_output=True, text=True)
            if process.returncode != 0:
                raise RuntimeError(f"FFmpeg failed: {process.stderr}")

            # Read the converted file
            with open(tmp_out_name, 'rb') as f:
                wav_bytes = f.read()
            return wav_bytes, 'audio/wav'
        finally:
            if tmp_in_name:
                try: os.unlink(tmp_in_name)
                except OSError: pass
            if tmp_out_name:
                try: os.unlink(tmp_out_name)
                except OSError: pass
                    
    return await asyncio.to_thread(_blocking_ffmpeg_operations)

async def convert_image_to_png(file_bytes: bytes, max_size: int = 1024) -> tuple[bytes, str]:
    """Convert image to PNG format and resize if needed"""
    def _blocking_pil_operations():
        img = Image.open(io.BytesIO(file_bytes))
        if img.mode in ('RGBA', 'P'):
            img = img.convert('RGB')
        width, height = img.size
        if width > max_size or height > max_size:
            ratio = min(max_size/width, max_size/height)
            new_width = int(width * ratio)
            new_height = int(height * ratio)
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        output = io.BytesIO()
        img.save(output, format='PNG', optimize=True)
        png_bytes = output.getvalue()
        return png_bytes, 'image/png'
        
    return await asyncio.to_thread(_blocking_pil_operations)

async def process_single_audio_file(audio_url: str, audio_file_identifier: str) -> Tuple[Optional[types.Part], str, str, Optional[str]]:
    """Process a single audio file. Returns (part, identifier, url, error_message_if_any)."""
    logger.info(f"📤 Processing {audio_file_identifier} audio file: {audio_url}")
    try:
        file_bytes, _ = await download_file_from_url(audio_url)
        wav_bytes, wav_mime_type = await convert_audio_to_wav(file_bytes)
        audio_part = types.Part.from_bytes(data=wav_bytes, mime_type=wav_mime_type)
        logger.info(f"✅ {audio_file_identifier.replace('_', ' ').capitalize()} audio processed successfully (MIME: {wav_mime_type}, Size: {len(wav_bytes)} bytes)")
        return audio_part, audio_file_identifier, audio_url, None
    except Exception as e:
        error_msg = f"Failed to process {audio_file_identifier} audio {audio_url}: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return None, audio_file_identifier, audio_url, error_msg

async def process_single_image_file(image_url: str, image_file_identifier: str) -> Tuple[Optional[types.Part], str, str, Optional[str]]:
    """Process a single image file. Returns (part, identifier, url, error_message_if_any)."""
    logger.info(f"📤 Processing {image_file_identifier} image file: {image_url}")
    try:
        file_bytes, _ = await download_file_from_url(image_url)
        png_bytes, png_mime_type = await convert_image_to_png(file_bytes)
        image_part = types.Part.from_bytes(data=png_bytes, mime_type=png_mime_type)
        logger.info(f"✅ {image_file_identifier.replace('_', ' ').capitalize()} image processed successfully (MIME: {png_mime_type}, Size: {len(png_bytes)} bytes)")
        return image_part, image_file_identifier, image_url, None
    except Exception as e:
        error_msg = f"Failed to process {image_file_identifier} image {image_url}: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return None, image_file_identifier, image_url, error_msg





@app.post("/api/generate-summary-stream")
# ### MODIFIED: Add `req: Request` to the function signature
async def generate_summary_stream(req: Request, request: GenerateSummaryStreamRequest):
    """Generate AI summary using Gemini 2.5 Flash with streaming response - Added from original and adapted"""
    GEMINI_MODEL_ID = "gemini-2.5-flash-lite-preview-06-17" # Standardized model ID

    logger.info(f"🎯 Received streaming request:")
    logger.info(f"   - Primary audio URL: {request.primary_audio_url}")
    logger.info(f"   - Additional audio URLs: {len(request.additional_audio_urls or [])}")
    logger.info(f"   - Image URLs: {len(request.image_urls or [])}")

    logger.info(f"   - Submitted by: {request.submitted_by}")
    logger.info(f"   - Consultation type: {request.consultation_type}")
    logger.info(f"   - Patient name: {request.patient_name}")
    logger.info(f"   - Created at: {request.created_at}")

    # Validate required fields
    if not request.primary_audio_url:
        logger.error("❌ Missing primary_audio_url in request")
        error_data = {"type": "error", "message": "Primary audio URL is required"}
        async def error_generator():
            yield f"data: {json.dumps(error_data)}\n\n"
        return StreamingResponse(error_generator(), media_type="text/plain")

    if not request.primary_audio_url.startswith(('http://', 'https://')):
        logger.error(f"❌ Invalid primary_audio_url format: {request.primary_audio_url}")
        error_data = {"type": "error", "message": "Primary audio URL must be a valid HTTP/HTTPS URL"}
        async def error_generator():
            yield f"data: {json.dumps(error_data)}\n\n"
        return StreamingResponse(error_generator(), media_type="text/plain")

    async def stream_generator():
        async with gemini_manager as client:
            if not client:
                # This path might be hard to hit if __aenter__ raises, but good for robustness
                error_data = {"type": "error", "message": "Gemini client not available"}
                yield f"data: {json.dumps(error_data)}\n\n"
                return

            logger.info(f"🎯 Processing consultation with {GEMINI_MODEL_ID} (Streaming)...")

            try:
                # ### MODIFIED: Pass the loaded config from app.state
                # Add debugging to understand what's happening in deployment
                logger.info(f"🔍 DEBUG: app.state.prompt_config type: {type(req.app.state.prompt_config)}")
                logger.info(f"🔍 DEBUG: Available templates: {list(req.app.state.prompt_config.__dict__.keys()) if hasattr(req.app.state.prompt_config, '__dict__') else 'No __dict__'}")

                prompt_config_dict = {k: v for k, v in req.app.state.prompt_config}
                logger.info(f"🔍 DEBUG: prompt_config_dict keys: {list(prompt_config_dict.keys())}")
                logger.info(f"🔍 DEBUG: Requested consultation_type: {request.consultation_type}")
                logger.info(f"🔍 DEBUG: Template found: {request.consultation_type in prompt_config_dict}")

                prompt = generate_prompt(
                    prompt_templates=prompt_config_dict,
                    submitted_by=request.submitted_by,
                    consultation_type=request.consultation_type,
                    doctor_notes=request.doctor_notes,
                    additional_notes=request.additional_notes,
                    patient_name=request.patient_name,
                    doctor_name=request.doctor_name,
                    created_at=request.created_at
                )
                contents: List[Any] = [prompt]
                files_processed = {"audio": 0, "images": 0, "errors": []}
                
                all_file_processing_tasks = []

                all_audio_urls = [request.primary_audio_url] + (request.additional_audio_urls or [])
                logger.info(f"🎵 Preparing {len(all_audio_urls)} audio file task(s) for streaming...")
                for i, audio_url in enumerate(all_audio_urls):
                    file_identifier = f"primary_audio" if i == 0 else f"additional_audio_{i}"
                    all_file_processing_tasks.append(
                        process_single_audio_file(audio_url, file_identifier)
                    )

                image_urls = request.image_urls or []
                logger.info(f"🖼️ Preparing {len(image_urls)} image file task(s) for streaming...")
                for i, image_url in enumerate(image_urls):
                    file_identifier = f"image_{i}"
                    all_file_processing_tasks.append(
                        process_single_image_file(image_url, file_identifier)
                    )

                if all_file_processing_tasks:
                    logger.info(f"🚀 Launching processing for {len(all_file_processing_tasks)} files concurrently for streaming...")
                    all_results = await asyncio.gather(*all_file_processing_tasks)

                    for result_tuple in all_results:
                        part, identifier, url, error_message = result_tuple
                        
                        if error_message:
                            files_processed["errors"].append({"file": url, "identifier": identifier, "error": error_message})
                        elif part:
                            contents.append(part)
                            if "audio" in identifier:
                                files_processed["audio"] += 1
                            elif "image" in identifier:
                                files_processed["images"] += 1
                        else: # Should not happen if error_message is always set on failure
                             unknown_error_msg = f"Unknown issue processing {identifier} from {url} for streaming."
                             logger.error(unknown_error_msg)
                             files_processed["errors"].append({"file": url, "identifier": identifier, "error": unknown_error_msg})


                total_files_successfully_processed = files_processed["audio"] + files_processed["images"]
                logger.info(f"🤖 Generating streaming summary with {GEMINI_MODEL_ID}...")
                logger.info(f"📊 Total files successfully processed for Gemini (streaming): {total_files_successfully_processed}")

                if files_processed["errors"]:
                    logger.warning(f"⚠️ {len(files_processed['errors'])} file(s) had processing errors (streaming): {files_processed['errors']}")
                
                if total_files_successfully_processed == 0 and not (request.doctor_notes or request.additional_notes):
                    if not any(isinstance(c, types.Part) for c in contents if c != prompt) and not (request.doctor_notes or request.additional_notes):
                        error_data = {"type": "error", "message": "No content (audio, image, or text notes) was available to generate a summary."}
                        yield f"data: {json.dumps(error_data)}\n\n"
                        return

                gemini_contents: List[types.Part | str] = []
                for item in contents:
                    if isinstance(item, (types.Part, str)):
                        gemini_contents.append(item)
                    else:
                        logger.warning(f"Skipping unexpected item type in contents (streaming): {type(item)}")

                # Send metadata first
                metadata = {
                    "type": "metadata",
                    "model": GEMINI_MODEL_ID,
                    "timestamp": datetime.now().isoformat(),
                    "files_processed": files_processed
                }
                yield f"data: {json.dumps(metadata)}\n\n"

                # Generate streaming content
                stream_response = await client.aio.models.generate_content_stream(
                    model=GEMINI_MODEL_ID,
                    contents=gemini_contents,
                    config=types.GenerateContentConfig(
                        thinking_config=types.ThinkingConfig(thinking_budget=8000)
                    )
                )

                summary_chunks = []
                
                async for chunk in stream_response:
                    if chunk.text:
                        summary_chunks.append(chunk.text)
                        chunk_data = {
                            "type": "chunk",
                            "text": chunk.text
                        }
                        
                        yield f"data: {json.dumps(chunk_data)}\n\n"
                        
                        # Force immediate delivery to prevent buffering
                        await asyncio.sleep(0.001)

                complete_summary = "".join(summary_chunks)
                completion_data = {
                    "type": "complete",
                    "summary": complete_summary,
                    "total_length": len(complete_summary)
                }
                yield f"data: {json.dumps(completion_data)}\n\n"

            except HTTPException as http_exc: # Handle HTTPExceptions raised during file processing etc.
                logger.error(f"❌ HTTPException during streaming summary: {http_exc.detail}", exc_info=True)
                error_data = {"type": "error", "message": f"Failed to generate summary: {http_exc.detail}"}
                yield f"data: {json.dumps(error_data)}\n\n"
            except Exception as e:
                logger.error(f"❌ Error generating streaming summary: {e}", exc_info=True)
                error_data = {"type": "error", "message": f"Failed to generate summary: {str(e)}"}
                yield f"data: {json.dumps(error_data)}\n\n"
    
    # Enhanced headers to prevent all forms of buffering
    return StreamingResponse(
        stream_generator(),
        media_type="text/plain", # As per original script
        headers={
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # Nginx buffering
            "X-Proxy-Buffering": "no",  # Generic proxy buffering
            "Transfer-Encoding": "chunked"  # Force chunked encoding
        }
    )

# Error handler - Added from original script
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"Global exception caught by handler: {exc}", exc_info=True)
    status_code = 500
    detail = str(exc)
    if isinstance(exc, HTTPException): # Use status_code from HTTPException if available
        status_code = exc.status_code
        detail = exc.detail
        
    return JSONResponse(
        status_code=status_code,
        content={"error": "Internal server error", "detail": detail}
    )



# Main execution block - Added from original script
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=int(os.getenv("PORT", 8080)), 
        log_level="info",
        # Optimize for streaming
        access_log=True,
        use_colors=True,
        # Force HTTP/1.1 for better streaming support
        http="h11",
        # Keep-alive timeout for streaming connections
        timeout_keep_alive=5
    )