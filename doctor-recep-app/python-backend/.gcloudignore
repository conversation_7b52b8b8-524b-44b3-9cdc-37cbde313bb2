# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
*.md

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Logs
*.log
logs/

# Environment files (keep .env.example but ignore actual .env files)
.env
.env.local
.env.production
.env.staging

# Testing
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/

# Documentation
docs/_build/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Node.js (in case there are any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker
.dockerignore

# Other
*.bak
*.orig
*.rej
*.md

# Deployment and build files (not needed in container)
deploy.sh
start.sh
.gcloudignore
