# Cloudflare Workers Environment Variables Setup Guide

This guide explains how to set up environment variables and secrets for your Celer AI app deployment on Cloudflare Workers.

## 🔐 **Required Secrets (Sensitive Data)**

These must be set using `wrangler secret put` command or Cloudflare Dashboard:

### **Supabase Configuration**
```bash
wrangler secret put NEXT_PUBLIC_SUPABASE_URL
# Value: https://edojplwmwtytpdssrxbh.supabase.co

wrangler secret put NEXT_PUBLIC_SUPABASE_ANON_KEY
# Value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVkb2pwbHdtd3R5dHBkc3NyeGJoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDY4MjcsImV4cCI6MjA2Njg4MjgyN30.dnzk7za0YQ-5R9Ses5r1DFNw74nWzCMoPeK0uXRoEyQ

wrangler secret put SUPABASE_SERVICE_ROLE_KEY
# Value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVkb2pwbHdtd3R5dHBkc3NyeGJoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwNjgyNywiZXhwIjoyMDY2ODgyODI3fQ.V_dz0QJMHEnBWqF-7XfrYqHIfZ_IRwNS6cuMH_0iOTQ
```

### **Authentication & Security**
```bash
wrangler secret put SESSION_SECRET
# Value: dbc4e13fd8fd3a091808cbe92d09f692a69fed648c257c2774fc9a7990f25fc6

wrangler secret put WEBHOOK_SECRET_TOKEN
# Value: 53651324f67a155e09e6b689fbba728e1a06aae9bfc5a98c8d54f0108883f67a

wrangler secret put INTERNAL_API_TOKEN
# Value: f6f5ee49be16ae3b8fcea657fdad2f718b7906061b483b68c89619b176e31772
```

### **SMS Service (VerifyNow)**
```bash
wrangler secret put VERIFYNOW_AUTH_TOKEN
# Value: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJDLTdBRTI1Mjc2RkM5MjRDRSIsImlhdCI6MTc1MTIwMjE3OCwiZXhwIjoxOTA4ODgyMTc4fQ.xDLDKjEDTcH8MsTW714QVVlHKAp_eOP_hvj3ZBHFGgL4SMnIIXl2-4fKbH3VsKav2BVpAo-RdprXdaGVdovePA

wrangler secret put VERIFYNOW_CUSTOMER_ID
# Value: C-7AE25276FC924CE
```

### **Email Service**
```bash
wrangler secret put RESEND_API_KEY
# Value: re_NMxerRQ8_NVFs1nFu9MFGC5QsGviAXjaA
```

### **~~Redis (Upstash)~~ - MIGRATED TO CLOUDFLARE KV**
```bash
# Redis credentials no longer needed - migrated to Cloudflare KV
# The KV namespace is configured in wrangler.toml as CELERAI_KV binding
# No secrets required for KV - it uses native Cloudflare Workers bindings
```

### **Cloudflare R2 Storage**
```bash
wrangler secret put R2_ACCESS_KEY_ID
# Value: 4dff08f96bf2f040b48bf3973813f7f0

wrangler secret put R2_SECRET_ACCESS_KEY
# Value: 6fb6d9a306ab8df2626e7519440424ed92ecf756d7c98514ff801c17268f9856

wrangler secret put R2_ACCOUNT_ID
# Value: 57014886c6cd87ebacf23a94e56a6e0c
```

### **External APIs**
```bash
wrangler secret put GEMINI_API_KEY
# Value: AIzaSyCuRoRkbCzzu9VDYfdgTj6rSWC_AKtUb94

wrangler secret put NEXT_PUBLIC_API_URL
# Value: https://doctor-recep-api-************.asia-south1.run.app
```

### **CMS (Sanity)**
```bash
wrangler secret put NEXT_PUBLIC_SANITY_PROJECT_ID
# Value: xpo3opql

wrangler secret put NEXT_PUBLIC_SANITY_DATASET
# Value: production

wrangler secret put NEXT_PUBLIC_SANITY_TOKEN
# Value: skNzZ3GJWN8nlU40kNfgrBFwJ5HGr2xVs5azIgr9l3JueGVgOLNVwiN6hUlVK6STGcW5OQWWwCMnE7nl7G0SQKeEUnZnDdRnDqhItLErtuBa5gkkduOqh5hsfnBGAVjNt65TnVcvFugvcY5jgLs68XaQbOx82hKp6lLRPwJLNHLnQ0lnPHBE
```

## 🌍 **Public Variables (Non-Sensitive)**

These are already configured in `wrangler.toml` under `[vars]` section:

```toml
[vars]
NODE_ENV = "production"
R2_BUCKET_NAME = "celerai-storage"
R2_PUBLIC_URL = "https://celerai.tallyup.pro"
```

## 📋 **Setup Commands**

### **Option 1: Set All Secrets at Once (Recommended)**
Create a script file `set-secrets.sh`:

```bash
#!/bin/bash
echo "Setting up Cloudflare Workers secrets..."

# Supabase
wrangler secret put NEXT_PUBLIC_SUPABASE_URL <<< "https://edojplwmwtytpdssrxbh.supabase.co"
wrangler secret put NEXT_PUBLIC_SUPABASE_ANON_KEY <<< "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVkb2pwbHdtd3R5dHBkc3NyeGJoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDY4MjcsImV4cCI6MjA2Njg4MjgyN30.dnzk7za0YQ-5R9Ses5r1DFNw74nWzCMoPeK0uXRoEyQ"
wrangler secret put SUPABASE_SERVICE_ROLE_KEY <<< "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVkb2pwbHdtd3R5dHBkc3NyeGJoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwNjgyNywiZXhwIjoyMDY2ODgyODI3fQ.V_dz0QJMHEnBWqF-7XfrYqHIfZ_IRwNS6cuMH_0iOTQ"

# Security
wrangler secret put SESSION_SECRET <<< "dbc4e13fd8fd3a091808cbe92d09f692a69fed648c257c2774fc9a7990f25fc6"
wrangler secret put WEBHOOK_SECRET_TOKEN <<< "53651324f67a155e09e6b689fbba728e1a06aae9bfc5a98c8d54f0108883f67a"
wrangler secret put INTERNAL_API_TOKEN <<< "f6f5ee49be16ae3b8fcea657fdad2f718b7906061b483b68c89619b176e31772"

echo "✅ All secrets configured successfully!"
```

### **Option 2: Use Cloudflare Dashboard**
1. Go to Cloudflare Dashboard
2. Navigate to Workers & Pages
3. Select your worker
4. Go to Settings > Environment Variables
5. Add each secret manually

## 🚀 **Deployment Steps**

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Build for Cloudflare:**
   ```bash
   npm run build:cf
   ```

3. **Set up secrets:**
   ```bash
   chmod +x set-secrets.sh
   ./set-secrets.sh
   ```

4. **Deploy to worker.celerai.live:**
   ```bash
   npm run deploy:cf
   ```

## 🔍 **Verification**

After deployment, verify that all environment variables are accessible:

```bash
# Check if secrets are set
wrangler secret list

# Test deployment
curl https://worker.celerai.live/api/health
```

## ⚠️ **Important Notes**

1. **Never commit secrets to git** - they're already in `.env.local` which is gitignored
2. **NEXT_PUBLIC_API_URL** should point to your Python backend URL
3. **Domain configuration** will be set up separately for `worker.celerai.live`
4. **Cloudflare Web Analytics** will be configured in the Cloudflare dashboard (no code changes needed)

## 🔄 **Updating Secrets**

To update a secret:
```bash
wrangler secret put SECRET_NAME
# Enter new value when prompted
```

To delete a secret:
```bash
wrangler secret delete SECRET_NAME
```
