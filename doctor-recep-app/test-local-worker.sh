#!/bin/bash

# Local Cloudflare Workers Testing Script for Celer AI
# This script builds and runs the worker locally for testing

set -e  # Exit on any error

echo "🧪 Starting local Cloudflare Workers testing for Celer AI..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if wrangler is installed
if ! command -v wrangler &> /dev/null; then
    echo -e "${RED}❌ Wrangler CLI not found. Installing...${NC}"
    npm install -g wrangler
fi

echo -e "${GREEN}✅ Wrangler CLI found${NC}"

# Build the application for Cloudflare Workers
echo -e "${BLUE}🏗️  Building application for Cloudflare Workers...${NC}"
npm run build:cf

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Build failed! Please check the errors above.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Build completed successfully${NC}"

# Create a local .dev.vars file for local development
echo -e "${BLUE}🔐 Setting up local environment variables...${NC}"

cat > .dev.vars << EOF
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://edojplwmwtytpdssrxbh.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVkb2pwbHdtd3R5dHBkc3NyeGJoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDY4MjcsImV4cCI6MjA2Njg4MjgyN30.dnzk7za0YQ-5R9Ses5r1DFNw74nWzCMoPeK0uXRoEyQ
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVkb2pwbHdtd3R5dHBkc3NyeGJoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwNjgyNywiZXhwIjoyMDY2ODgyODI3fQ.V_dz0QJMHEnBWqF-7XfrYqHIfZ_IRwNS6cuMH_0iOTQ

# Authentication & Security
SESSION_SECRET=dbc4e13fd8fd3a091808cbe92d09f692a69fed648c257c2774fc9a7990f25fc6
WEBHOOK_SECRET_TOKEN=53651324f67a155e09e6b689fbba728e1a06aae9bfc5a98c8d54f0108883f67a
INTERNAL_API_TOKEN=f6f5ee49be16ae3b8fcea657fdad2f718b7906061b483b68c89619b176e31772

# SMS Service (VerifyNow)
VERIFYNOW_AUTH_TOKEN=eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJDLTdBRTI1Mjc2RkM5MjRDRSIsImlhdCI6MTc1MTIwMjE3OCwiZXhwIjoxOTA4ODgyMTc4fQ.xDLDKjEDTcH8MsTW714QVVlHKAp_eOP_hvj3ZBHFGgL4SMnIIXl2-4fKbH3VsKav2BVpAo-RdprXdaGVdovePA
VERIFYNOW_CUSTOMER_ID=C-7AE25276FC924CE

# Email Service
RESEND_API_KEY=re_NMxerRQ8_NVFs1nFu9MFGC5QsGviAXjaA

# Redis credentials no longer needed - migrated to Cloudflare KV
# UPSTASH_REDIS_REST_URL=https://redis-16389.c301.ap-south-1-1.ec2.redns.redis-cloud.com
# UPSTASH_REDIS_REST_TOKEN=iAa1TCfifiAHaFP8QjXVQKmIHJXX4yZa

# Cloudflare R2 Storage
R2_ACCESS_KEY_ID=4dff08f96bf2f040b48bf3973813f7f0
R2_SECRET_ACCESS_KEY=6fb6d9a306ab8df2626e7519440424ed92ecf756d7c98514ff801c17268f9856
R2_ACCOUNT_ID=57014886c6cd87ebacf23a94e56a6e0c
R2_BUCKET_NAME=celerai-storage
R2_PUBLIC_URL=https://celerai.tallyup.pro

# External APIs
GEMINI_API_KEY=AIzaSyCuRoRkbCzzu9VDYfdgTj6rSWC_AKtUb94
NEXT_PUBLIC_API_URL=https://doctor-recep-api-************.asia-south1.run.app

# CMS (Sanity)
NEXT_PUBLIC_SANITY_PROJECT_ID=xpo3opql
NEXT_PUBLIC_SANITY_DATASET=production
NEXT_PUBLIC_SANITY_TOKEN=skNzZ3GJWN8nlU40kNfgrBFwJ5HGr2xVs5azIgr9l3JueGVgOLNVwiN6hUlVK6STGcW5OQWWwCMnE7nl7G0SQKeEUnZnDdRnDqhItLErtuBa5gkkduOqh5hsfnBGAVjNt65TnVcvFugvcY5jgLs68XaQbOx82hKp6lLRPwJLNHLnQ0lnPHBE

# Environment
NODE_ENV=development
EOF

echo -e "${GREEN}✅ Local environment variables configured${NC}"

# Check if .open-next directory exists
if [ ! -d ".open-next" ]; then
    echo -e "${RED}❌ .open-next directory not found. Build may have failed.${NC}"
    exit 1
fi

echo -e "${BLUE}🚀 Starting local Cloudflare Workers development server...${NC}"
echo -e "${YELLOW}📋 Local Testing Information:${NC}"
echo -e "   • Local URL: http://localhost:8787"
echo -e "   • Environment: development"
echo -e "   • All secrets loaded from .dev.vars"
echo ""
echo -e "${YELLOW}🧪 Test these endpoints:${NC}"
echo -e "   • Homepage: http://localhost:8787"
echo -e "   • API Health: http://localhost:8787/api/health (if exists)"
echo -e "   • Dashboard: http://localhost:8787/dashboard"
echo -e "   • Admin: http://localhost:8787/admin"
echo ""
echo -e "${GREEN}✅ Press Ctrl+C to stop the local server${NC}"
echo ""

# Start the local development server
wrangler dev --local --port 8787

echo -e "${BLUE}🛑 Local development server stopped${NC}"
