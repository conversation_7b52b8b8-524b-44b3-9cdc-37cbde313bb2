<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Stream Test</title>
    <style>
        body { font-family: monospace; padding: 20px; background: #1a1a1a; color: #00ff00; }
        .container { max-width: 800px; margin: 0 auto; }
        .log { background: #000; padding: 10px; border-radius: 5px; margin: 10px 0; min-height: 200px; overflow-y: auto; }
        .chunk { margin: 2px 0; padding: 2px 5px; background: #333; border-left: 3px solid #00ff00; }
        .error { border-left-color: #ff0000; color: #ff0000; }
        .info { border-left-color: #0088ff; color: #0088ff; }
        button { background: #00ff00; color: #000; border: none; padding: 10px 20px; margin: 5px; cursor: pointer; }
        button:disabled { background: #666; color: #999; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Frontend Stream Debugging Tool</h1>
        <p>This tests if your Worker is streaming correctly to the frontend.</p>
        
        <button id="testBtn" onclick="testStream()">🚀 Test Stream</button>
        <button onclick="clearLog()">🧹 Clear Log</button>
        
        <div id="log" class="log"></div>
        
        <h3>📊 Results Summary:</h3>
        <div id="summary" class="log"></div>
    </div>

    <script>
        let chunkCount = 0;
        let startTime = 0;
        let firstChunkTime = 0;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const div = document.createElement('div');
            div.className = `chunk ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(div);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateSummary(message) {
            document.getElementById('summary').innerHTML = `<div class="chunk info">${message}</div>`;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('summary').innerHTML = '';
            chunkCount = 0;
        }

        async function testStream() {
            const btn = document.getElementById('testBtn');
            btn.disabled = true;
            btn.textContent = '🔄 Testing...';
            
            clearLog();
            chunkCount = 0;
            startTime = Date.now();
            firstChunkTime = 0;

            log('🚀 Starting stream test...', 'info');
            
            // Test payload - minimal valid request
            const testPayload = {
                primary_audio_url: "https://celerai.tallyup.pro/test-audio.wav",
                additional_audio_urls: [],
                image_urls: [],
                submitted_by: "doctor",
                consultation_type: "outpatient",
                patient_name: "Test Patient"
            };

            try {
                log('📡 Sending request to Worker...', 'info');
                
                const response = await fetch('/api/generate-summary-stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testPayload)
                });

                log(`📊 Response status: ${response.status}`, response.ok ? 'info' : 'error');
                log(`📋 Content-Type: ${response.headers.get('Content-Type')}`, 'info');
                log(`🆔 Job-ID: ${response.headers.get('X-Job-ID')}`, 'info');

                if (!response.ok) {
                    const errorText = await response.text();
                    log(`❌ Error response: ${errorText}`, 'error');
                    updateSummary('❌ Request failed - check backend connectivity');
                    return;
                }

                if (!response.body) {
                    log('❌ No response body available', 'error');
                    updateSummary('❌ No response body - streaming not supported');
                    return;
                }

                log('🔄 Starting to read stream...', 'info');
                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        const totalTime = Date.now() - startTime;
                        log(`✅ Stream finished after ${totalTime}ms`, 'info');
                        
                        if (chunkCount === 0) {
                            updateSummary('❌ NO CHUNKS RECEIVED - Stream is not working');
                        } else if (chunkCount === 1) {
                            updateSummary('⚠️ ONLY ONE CHUNK - Likely buffered, not streaming');
                        } else {
                            const avgChunkTime = firstChunkTime ? (totalTime - firstChunkTime) / (chunkCount - 1) : 0;
                            updateSummary(`✅ STREAMING WORKS! ${chunkCount} chunks, avg ${avgChunkTime.toFixed(0)}ms between chunks`);
                        }
                        break;
                    }

                    chunkCount++;
                    const currentTime = Date.now();
                    if (chunkCount === 1) {
                        firstChunkTime = currentTime;
                    }
                    
                    const chunk = decoder.decode(value, { stream: true });
                    const elapsed = currentTime - startTime;
                    
                    log(`📦 Chunk ${chunkCount} (${elapsed}ms): ${chunk.substring(0, 100)}${chunk.length > 100 ? '...' : ''}`, 'info');
                    
                    // Parse SSE format
                    const lines = chunk.split('\n');
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                if (data.type === 'chunk' && data.text) {
                                    log(`💬 AI Text: ${data.text.substring(0, 50)}...`, 'info');
                                }
                            } catch (e) {
                                // Ignore parse errors
                            }
                        }
                    }
                }

            } catch (error) {
                log(`❌ Stream error: ${error.message}`, 'error');
                updateSummary(`❌ Stream failed: ${error.message}`);
            } finally {
                btn.disabled = false;
                btn.textContent = '🚀 Test Stream';
            }
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            log('🔧 Frontend Stream Test Tool loaded', 'info');
            log('📝 Click "Test Stream" to debug your streaming implementation', 'info');
        });
    </script>
</body>
</html>
