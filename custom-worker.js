//@ts-expect-error: Will be resolved by wrangler build
import { fetchImage } from "./.open-next/cloudflare/images.js";
//@ts-expect-error: Will be resolved by wrangler build
import { runWithCloudflareRequestContext } from "./.open-next/cloudflare/init.js";
// @ts-expect-error: Will be resolved by wrangler build
import { handler as middlewareHandler } from "./.open-next/middleware/handler.mjs";

// 🚀 DURABLE OBJECTS: Export OpenNext.js default DOs + our custom streaming handler
//@ts-expect-error: Will be resolved by wrangler build
export { DOQueueHandler } from "./.open-next/.build/durable-objects/queue.js";
//@ts-expect-error: Will be resolved by wrangler build
export { DOShardedTagCache } from "./.open-next/.build/durable-objects/sharded-tag-cache.js";
//@ts-expect-error: Will be resolved by wrangler build
export { BucketCachePurge } from "./.open-next/.build/durable-objects/bucket-cache-purge.js";
// 🚀 OUR CUSTOM STREAMING HANDLER
export { SummaryStreamHandler } from "./.open-next/durable-objects/summary-stream-handler.js";

export default {
    async fetch(request, env, ctx) {
        return runWithCloudflareRequestContext(request, env, ctx, async () => {
            const url = new URL(request.url);
            
            // 🚀 STREAMING INTERCEPTOR: Route streaming requests to Durable Objects
            // This bypasses OpenNext.js to prevent SSE corruption
            if (url.pathname === '/api/generate-summary-stream' && request.method === 'POST') {
                console.log('🎯 Intercepting streaming request for Durable Objects');
                const durableObjectId = env.SUMMARY_STREAM_HANDLER.idFromName('summary-stream');
                const durableObject = env.SUMMARY_STREAM_HANDLER.get(durableObjectId);
                return durableObject.fetch(request);
            }
            
            // Serve images in development.
            // Note: "/cdn-cgi/image/..." requests do not reach production workers.
            if (url.pathname.startsWith("/cdn-cgi/image/")) {
                const m = url.pathname.match(/\/cdn-cgi\/image\/.+?\/(?<url>.+)$/);
                if (m === null) {
                    return new Response("Not Found!", { status: 404 });
                }
                const imageUrl = m.groups.url;
                return imageUrl.match(/^https?:\/\//)
                    ? fetch(imageUrl, { cf: { cacheEverything: true } })
                    : env.ASSETS?.fetch(new URL(`/${imageUrl}`, url));
            }
            // Fallback for the Next default image loader.
            if (url.pathname === `${globalThis.__NEXT_BASE_PATH__}/_next/image`) {
                const imageUrl = url.searchParams.get("url") ?? "";
                return fetchImage(env.ASSETS, imageUrl);
            }
            // - `Request`s are handled by the Next server
            const reqOrResp = await middlewareHandler(request, env, ctx);
            if (reqOrResp instanceof Response) {
                return reqOrResp;
            }
            // @ts-expect-error: resolved by wrangler build
            const { handler } = await import("./.open-next/server-functions/default/handler.mjs");
            return handler(reqOrResp, env, ctx);
        });
    },
};
